// ===== GESTION DES VENTES =====

let ventesTable;
let currentVenteId = null;
let venteDetails = [];

$(document).ready(function() {
    // Initialisation
    initDataTables();
    loadVentes();
    loadClients();
    loadProduits();
    loadDepots();
    loadUnites();
    
    // Event handlers
    setupEventHandlers();
    
    // Générer la référence de vente
    generateReferenceVente();
    
    // Initialiser les dates automatiquement
    setDefaultDates();
    
    // Initialiser Select2
    initSelect2();
});

// ===== INITIALISATION =====

function initDataTables() {
    if ($('#tableVentes').length && !$.fn.DataTable.isDataTable('#tableVentes')) {
        ventesTable = $('#tableVentes').DataTable({
            pageLength: 20,
            lengthMenu: [30, 100, 200],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            columnDefs: [
                { orderable: false, targets: [0, 7] }, // Colonnes checkbox et actions
                { className: "text-center", targets: [0, 5] }, // Statut centré
                { className: "text-end", targets: [6] } // Montant aligné à droite
            ]
        });
    }
}

// ===== CHARGEMENT DES DONNÉES =====

function loadVentes() {
    // Vérifier d'abord si les tables existent
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT COUNT(*) as table_exists 
              FROM information_schema.tables 
              WHERE table_schema = DATABASE() 
              AND table_name IN ('ventes_entete', 'ventes_details')`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success && response.data.length > 0 && response.data[0].table_exists == 2) {
            // Les tables existent, charger les ventes
            loadVentesData();
        } else {
            // Les tables n'existent pas, afficher un message
            showAlert('Configuration requise', 
                'Les tables de vente n\'existent pas encore. Veuillez exécuter le script de configuration.', 
                'warning');
            displayVentes([]); // Afficher un tableau vide
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors de la vérification des tables:', error);
        displayVentes([]); // Afficher un tableau vide en cas d'erreur
    });
}

function loadVentesData() {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                ve.id,
                ve.facture_numero as reference_vente,
                c.nom as client_nom,
                ve.date_vente,
                ve.facture_date,
                ve.statut,
                ve.total_montant,
                ve.valeur_euro,
                ve.valeur_ar
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              ORDER BY ve.date_creation DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            displayVentes(response.data);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des ventes:', error);
        showAlert('Erreur', 'Impossible de charger les ventes', 'error');
    });
}

function displayVentes(ventes) {
    if (ventesTable) {
        ventesTable.clear();
        ventes.forEach(vente => {
            const statutBadge = getStatutBadge(vente.statut);
            const montantFormatted = parseFloat(vente.total_montant || 0).toLocaleString() + ' Ar';
            
            // Générer les boutons selon le statut
            let actionButtons = generateActionButtons(vente);
            
            ventesTable.row.add([
                `<input type="checkbox" value="${vente.id}">`,
                vente.reference_vente || 'N/A',
                vente.client_nom || 'N/A',
                vente.date_vente || 'N/A',
                vente.facture_date || 'N/A',
                statutBadge,
                montantFormatted,
                actionButtons
            ]);
        });
        ventesTable.draw();
        
        // Mettre à jour les statistiques
        updateStatistics(ventes);
    }
}

function displayVentesFiltered(ventes) {
    if (ventesTable) {
        ventesTable.clear();
        ventes.forEach(vente => {
            const statutBadge = getStatutBadge(vente.statut);
            const montantFormatted = parseFloat(vente.total_montant || 0).toLocaleString() + ' Ar';
            
            // Générer les boutons selon le statut
            let actionButtons = generateActionButtons(vente);
            
            ventesTable.row.add([
                `<input type="checkbox" value="${vente.id}">`,
                vente.reference_vente || 'N/A',
                vente.client_nom || 'N/A',
                vente.date_vente || 'N/A',
                vente.facture_date || 'N/A',
                statutBadge,
                montantFormatted,
                actionButtons
            ]);
        });
        ventesTable.draw();
        
        // Ne pas mettre à jour les statistiques lors du filtrage
        // Les statistiques doivent toujours refléter toutes les données
    }
}

function updateStatistics(ventes) {
    // Calculer les statistiques sur toutes les données, pas seulement celles filtrées
    loadAllStatistics();
}

function loadAllStatistics() {
    // Charger toutes les données pour les statistiques (sans filtres)
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: `SELECT 
                ve.id,
                ve.statut,
                ve.client_id,
                COALESCE(ve.total_montant, 0) as montant_total
              FROM ventes_entete ve
              ORDER BY ve.date_creation DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            const allVentes = response.data;
            
            const totalVentes = allVentes.length;
            const montantTotal = allVentes.reduce((sum, vente) => sum + parseFloat(vente.montant_total || 0), 0);
            const clients = new Set(allVentes.map(vente => vente.client_id)).size;
            const enAttente = allVentes.filter(vente => vente.statut === 'EN ATTENTE').length;
            const aFacturer = allVentes.filter(vente => vente.statut === 'EN COURS').length;
            const payes = allVentes.filter(vente => vente.statut === 'PAYE').length;
            
            $('#statTotalVentes').text(totalVentes);
            $('#statMontantTotal').text(montantTotal.toLocaleString() + ' Ar');
            $('#statClients').text(clients);
            $('#statEnAttente').text(enAttente);
            $('#statAFacturer').text(aFacturer);
            $('#statPayes').text(payes);
        }
    }).fail(function() {
        console.error('Erreur lors du chargement des statistiques');
    });
}

function generateActionButtons(vente) {
    let buttons = '';

    // Bouton Voir (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-info btn-view-vente" data-id="${vente.id}" title="Voir détails">
        <i class="fas fa-eye"></i>
    </button> `;

    // Bouton Modifier (seulement si EN COURS)
    if (vente.statut === 'EN COURS') {
        buttons += `<button class="btn btn-sm btn-outline-warning btn-edit-vente" data-id="${vente.id}" title="Modifier">
            <i class="fas fa-edit"></i>
        </button> `;
    }

    // Bouton Mettre en attente (seulement si EN COURS)
    if (vente.statut === 'EN COURS') {
        buttons += `<button class="btn btn-sm btn-outline-warning btn-pending-vente" data-id="${vente.id}" title="Mettre en attente">
            <i class="fas fa-clock"></i>
        </button> `;
    }

    // Bouton Facturer (seulement si EN ATTENTE)
    if (vente.statut === 'EN ATTENTE') {
        buttons += `<button class="btn btn-sm btn-outline-primary btn-invoice-vente" data-id="${vente.id}" title="Facturer">
            <i class="fas fa-file-invoice"></i>
        </button> `;
    }

    // Bouton Paiement supprimé - utiliser la gestion des paiements de ventes

    // Bouton Annuler (seulement si pas ANNULE et pas PAYE)
    if (vente.statut !== 'ANNULE' && vente.statut !== 'PAYE') {
        buttons += `<button class="btn btn-sm btn-outline-danger btn-cancel-vente" data-id="${vente.id}" title="Annuler">
            <i class="fas fa-times"></i>
        </button> `;
    }

    // Bouton Supprimer (seulement si EN COURS)
    if (vente.statut === 'EN COURS') {
        buttons += `<button class="btn btn-sm btn-outline-danger btn-delete-vente" data-id="${vente.id}" title="Supprimer">
            <i class="fas fa-trash"></i>
        </button> `;
    }

    // Bouton Envoyer par email (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-info btn-email-vente" data-id="${vente.id}" title="Envoyer par email">
        <i class="fas fa-envelope"></i>
    </button> `;

    // Bouton Imprimer (seulement si PAYE - comme dans gestion_achats)
    if (vente.statut === 'PAYE') {
        buttons += `<button class="btn btn-sm btn-outline-secondary btn-print-vente" data-id="${vente.id}" title="Imprimer facture">
            <i class="fas fa-print"></i>
        </button>`;
    }

    return buttons;
}

function getStatutBadge(statut) {
    // Utiliser les nouvelles valeurs enum de la DB: 'EN COURS','EN ATTENTE','FACTURE','PAYE','ANNULE'
    const badges = {
        'EN COURS': '<span class="badge bg-info status-badge">En cours</span>',
        'EN ATTENTE': '<span class="badge bg-warning status-badge">En attente</span>',
        'FACTURE': '<span class="badge bg-primary status-badge">Facturé</span>',
        'PAYE': '<span class="badge bg-success status-badge">Payé</span>',
        'ANNULE': '<span class="badge bg-dark status-badge">Annulé</span>'
    };
    return badges[statut] || '<span class="badge bg-secondary status-badge">Inconnu</span>';
}

// ===== CHARGEMENT DES DONNÉES DE RÉFÉRENCE =====

function loadClients() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'clients'
    }).done(function(response) {
        if (response.success) {
            const select = $('#clientVente');
            const filterSelect = $('#filterClient');

            select.empty().append('<option value="">Sélectionner un client</option>');
            filterSelect.empty().append('<option value="">Tous les clients</option>');

            response.data.forEach(client => {
                select.append(`<option value="${client.id}">${client.nom} (${client.type_client})</option>`);
                filterSelect.append(`<option value="${client.id}">${client.nom} (${client.type_client})</option>`);
            });
        }
    });
}

function loadProduits() {
    // Charger les produits avec leurs informations de stock détaillées
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT
                p.id,
                p.nom,
                p.type,
                p.certification,
                p.prix_vente,
                p.stock_min,
                p.stock_max,
                p.unite_stock_id,
                c.libelle as categorie_nom,
                us.libelle as unite_stock_nom,
                p.presentation_id,
                pr.libelle as presentation_libelle,
                p.qte_presentation,
                p.forme_id,
                f.libelle as forme_libelle,
                p.qte_forme,
                ps.id as stock_id,
                ps.quantite,
                ps.lot_numero,
                ps.date_entree,
                ps.date_expiration,
                d.id as depot_id,
                d.libelle as depot_nom
              FROM produits p
              LEFT JOIN categorie c ON p.categorie_id = c.id
              LEFT JOIN unites us ON p.unite_stock_id = us.id
              LEFT JOIN presentation pr ON p.presentation_id = pr.id
              LEFT JOIN forme f ON p.forme_id = f.id
              LEFT JOIN produits_stock ps ON p.id = ps.produit_id
              LEFT JOIN depot d ON ps.depot_id = d.id
              WHERE p.classe = 'PRODUITS'
              ORDER BY p.nom, ps.lot_numero`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            window.produitsCache = {};
            window.produitsStockCache = {};

            // Organiser les données par produit
            response.data.forEach(row => {
                const produitId = row.id;

                // Initialiser le produit s'il n'existe pas
                if (!window.produitsCache[produitId]) {
                    window.produitsCache[produitId] = {
                        id: row.id,
                        nom: row.nom,
                        type: row.type,
                        certification: row.certification,
                        prix_vente: row.prix_vente,
                        stock_min: row.stock_min,
                        stock_max: row.stock_max,
                        categorie_nom: row.categorie_nom,
                        unite_stock_id: row.unite_stock_id,
                        unite_stock_nom: row.unite_stock_nom,
                        presentation_libelle: row.presentation_libelle,
                        qte_presentation: row.qte_presentation,
                        forme_libelle: row.forme_libelle,
                        qte_forme: row.qte_forme,
                        lots: []
                    };
                }

                // Ajouter les informations de stock si elles existent
                if (row.stock_id && row.quantite > 0) {
                    // Convertir la quantité de stock vers les tonnes pour l'affichage
                    const quantiteTonnes = convertStockToTonnes(
                        parseFloat(row.quantite),
                        row.unite_stock_nom,
                        row.qte_forme || 67
                    );

                    window.produitsCache[produitId].lots.push({
                        stock_id: row.stock_id,
                        quantite: quantiteTonnes, // Quantité convertie en tonnes
                        quantite_stock_originale: parseFloat(row.quantite), // Quantité originale dans l'unité de stock
                        lot_numero: row.lot_numero,
                        date_entree: row.date_entree,
                        date_expiration: row.date_expiration,
                        depot_id: row.depot_id,
                        depot_nom: row.depot_nom
                    });
                }
            });

            console.log('Produits avec stock chargés:', window.produitsCache);
        } else {
            showAlert('Erreur', 'Impossible de charger les produits: ' + response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger les produits', 'error');
    });
}

function loadDepots() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'depot'
    }).done(function(response) {
        if (response.success) {
            window.depotsCache = {};
            response.data.forEach(depot => {
                window.depotsCache[depot.id] = depot;
            });
        }
    });
}

function loadUnites() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'unites'
    }).done(function(response) {
        if (response.success) {
            window.unitesCache = {};
            response.data.forEach(unite => {
                window.unitesCache[unite.id] = unite;
            });
        }
    });
}

// ===== GESTION DES ÉVÉNEMENTS =====

function setupEventHandlers() {
    // Boutons d'action
    $(document).on('click', '.btn-view-vente', function() {
        const id = $(this).data('id');
        viewVente(id);
    });

    $(document).on('click', '.btn-edit-vente', function() {
        const id = $(this).data('id');
        editVente(id);
    });

    $(document).on('click', '.btn-delete-vente', function() {
        const id = $(this).data('id');
        deleteVente(id);
    });

    // Nouveaux boutons d'action pour les ventes
    $(document).on('click', '.btn-pending-vente', function() {
        const id = $(this).data('id');
        pendingVente(id);
    });

    $(document).on('click', '.btn-invoice-vente', function() {
        const id = $(this).data('id');
        invoiceVente(id);
    });

    // Événement paiement supprimé - utiliser la gestion des paiements de ventes

    $(document).on('click', '.btn-cancel-vente', function() {
        const id = $(this).data('id');
        cancelVente(id);
    });

    $(document).on('click', '.btn-email-vente', function() {
        const id = $(this).data('id');
        sendEmailVente(id);
    });

    $(document).on('click', '.btn-print-vente', function() {
        const id = $(this).data('id');
        printVente(id);
    });

    // Bouton d'annulation multiple
    $('#btnCancelVentes').on('click', function() {
        const selectedIds = getSelectedVenteIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins une vente', 'warning');
            return;
        }
        cancelVentes(selectedIds);
    });

    // Filtres
    $('#btnApplyFilters').on('click', function() {
        applyFilters();
    });

    $('#btnResetFilters').on('click', function() {
        resetFilters();
    });

    // Application automatique des filtres lors du changement
    $('#filterStatus, #filterDateFrom, #filterDateTo, #filterClient').on('change', function() {
        applyFilters();
    });

    // Sauvegarde de vente
    $('#btnSaveVente').on('click', function() {
        saveVente();
    });

    // Bouton Nouvelle Vente
    $('[data-bs-target="#modalVente"]').click(function() {
        currentVenteId = null;
        resetVenteForm();
        // Mettre à jour le titre du modal et le bouton
        $('#modalVenteTitle').html('<i class="fas fa-chart-line"></i> Nouvelle Vente');
        $('#btnSaveText').text('Enregistrer la Vente');
    });

    // Ajout de produit - afficher la section de sélection
    $('#btnAddProduitVente').on('click', function() {
        showProductSelectionSection();
    });

    // Annuler la sélection de produit
    $('#btnCancelProductSelection').on('click', function() {
        hideProductSelectionSection();
    });

    // Suppression de produit
    $(document).on('click', '.btn-remove-produit', function() {
        const row = $(this).closest('tr');
        const produitSelect = row.find('.produit-select');
        const produitNom = produitSelect.find('option:selected').text() || 'ce produit';

        Swal.fire({
            title: 'Supprimer le produit',
            text: `Êtes-vous sûr de vouloir supprimer ${produitNom} de cette vente ?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6'
        }).then((result) => {
            if (result.isConfirmed) {
                row.remove();
                updateTotals();
                showAlert('Succès', 'Produit supprimé de la vente', 'success');
            }
        });
    });

    // Changement de quantité/prix avec validation de stock
    $(document).on('input', '.qte-tonnes, .prix-unitaire', function() {
        const row = $(this).closest('tr');

        // Validation de stock pour les quantités
        if ($(this).hasClass('qte-tonnes')) {
            validateStockQuantity(row);
        }

        updateRowCalculations(row);
        updateTotals();
    });

    // Changement de cours de devise
    $('#coursDevise').on('input', function() {
        updateCurrencyConversion();
    });

    // Changement de mode de paiement
    $('#modePaiement').on('change', function() {
        const mode = $(this).val();
        const referenceGroup = $('#referencePaiementGroup');
        const referenceLabel = $('#referencePaiementLabel');

        if (mode === 'CHEQUE') {
            referenceGroup.show();
            referenceLabel.text('Numéro de chèque');
            $('#referencePaiement').attr('placeholder', 'Numéro de chèque');
        } else if (mode === 'VIREMENT') {
            referenceGroup.show();
            referenceLabel.text('Référence virement');
            $('#referencePaiement').attr('placeholder', 'Référence virement');
        } else {
            referenceGroup.hide();
        }
    });

    // Confirmation de paiement supprimée - utiliser gestion_paiements_ventes.php
}

// ===== FONCTIONS UTILITAIRES =====

function generateReferenceVente() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    const reference = `VTE-${year}${month}${day}-${hours}${minutes}`;
    $('#referenceVente').val(reference);
}

function setDefaultDates() {
    const today = new Date().toISOString().split('T')[0];
    $('#dateVente').val(today);
    $('#dateFacture').val(today);
    $('#datePaiement').val(today);
}

function initSelect2() {
    $('#clientVente').select2({
        theme: 'bootstrap-5',
        dropdownParent: $('#modalVente'),
        placeholder: 'Sélectionner un client',
        allowClear: true
    });

    $('#filterClient').select2({
        theme: 'bootstrap-5',
        placeholder: 'Tous les clients',
        allowClear: true
    });
}

function resetVenteForm() {
    $('#formVenteEntete')[0].reset();
    $('#tableDetailsVente tbody').empty();
    venteDetails = [];
    generateReferenceVente();
    setDefaultDates();
    updateTotals();

    // Réinitialiser Select2
    $('#clientVente').val(null).trigger('change');
}

function getSelectedVenteIds() {
    const selectedIds = [];
    $('#tableVentes tbody input[type="checkbox"]:checked').each(function() {
        selectedIds.push($(this).val());
    });
    return selectedIds;
}

function showAlert(title, message, type = 'info') {
    Swal.fire({
        title: title,
        text: message,
        icon: type,
        confirmButtonText: 'OK'
    });
}

function showConfirm(title, message, confirmText = 'Confirmer', cancelText = 'Annuler') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: confirmText,
        cancelButtonText: cancelText,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33'
    });
}

// ===== GESTION DES PRODUITS =====

// ===== NOUVELLE INTERFACE DE SÉLECTION DE PRODUITS =====

function showProductSelectionSection() {
    console.log('Affichage de la section de sélection de produits');

    // Afficher la section de sélection
    $('#productSelectionSection').slideDown();

    // Charger les dépôts pour le filtre
    loadDepotsForFilter();

    // Afficher les produits
    displayProductsInSection();

    // Réinitialiser les filtres
    resetProductFilters();
}

function hideProductSelectionSection() {
    // Masquer la section de sélection
    $('#productSelectionSection').slideUp();

    // Nettoyer le contenu
    $('#produitsContainer').empty();
}

function loadDepotsForFilter() {
    if (window.depotsCache) {
        const select = $('#filterDepot');
        select.empty().append('<option value="">Tous les dépôts</option>');

        Object.values(window.depotsCache).forEach(depot => {
            select.append(`<option value="${depot.id}">${depot.libelle}</option>`);
        });
    }
}

function displayProductsInSection() {
    console.log('Affichage des produits dans la section');

    const container = $('#produitsContainer');
    const noProductsMessage = $('#noProductsMessage');

    container.empty();

    if (!window.produitsCache || Object.keys(window.produitsCache).length === 0) {
        console.log('Aucun produit dans le cache, affichage du message');
        noProductsMessage.removeClass('d-none');
        return;
    }

    console.log('Produits disponibles:', Object.keys(window.produitsCache).length);
    noProductsMessage.addClass('d-none');

    Object.values(window.produitsCache).forEach(produit => {
        const productCard = createProductCard(produit);
        container.append(productCard);
    });

    // Ajouter les événements
    setupProductCardEvents();
}

function createProductCard(produit) {
    // Calculer le stock total
    const stockTotal = produit.lots.reduce((total, lot) => total + lot.quantite, 0);

    // Déterminer le statut du stock
    let stockStatus = 'stock-high';
    let stockText = 'Stock suffisant';

    if (stockTotal === 0) {
        stockStatus = 'stock-low';
        stockText = 'Rupture de stock';
    } else if (stockTotal <= produit.stock_min) {
        stockStatus = 'stock-low';
        stockText = 'Stock faible';
    } else if (stockTotal <= produit.stock_min * 2) {
        stockStatus = 'stock-medium';
        stockText = 'Stock moyen';
    }

    // Créer les cartes de lots
    let lotsHtml = '';
    if (produit.lots.length > 0) {
        produit.lots.forEach(lot => {
            const lotStockStatus = lot.quantite <= 10 ? 'stock-low' :
                                 lot.quantite <= 50 ? 'stock-medium' : 'stock-high';

            lotsHtml += `
                <div class="lot-item" data-lot-id="${lot.stock_id}" data-produit-id="${produit.id}">
                    <div class="lot-header">
                        <span class="lot-number">Lot: ${lot.lot_numero || 'N/A'}</span>
                        <span class="stock-badge ${lotStockStatus}">${lot.quantite} ${produit.unite_stock_nom}</span>
                    </div>
                    <div class="lot-details">
                        <div class="row">
                            <div class="col-6">
                                <small><i class="fas fa-warehouse"></i> ${lot.depot_nom}</small>
                            </div>
                            <div class="col-6">
                                <small><i class="fas fa-calendar"></i> ${formatDate(lot.date_entree)}</small>
                            </div>
                        </div>
                        ${lot.date_expiration ? `
                        <div class="mt-1">
                            <small><i class="fas fa-clock text-warning"></i> Exp: ${formatDate(lot.date_expiration)}</small>
                        </div>
                        ` : ''}
                    </div>
                    <div class="quantity-selector">
                        <div class="mb-2">
                            <label class="form-label small mb-1">
                                <strong>Quantité (tonnes)</strong>
                                <span class="text-muted">- Max: ${lot.quantite.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes</span>
                            </label>
                            <input type="number" class="form-control quantity-input"
                                   placeholder="0.000" min="0" max="${lot.quantite}" step="0.001"
                                   data-max-stock="${lot.quantite}"
                                   data-stock-original="${lot.quantite_stock_originale}"
                                   data-unite-stock="${produit.unite_stock_nom}"
                                   data-qte-forme="${produit.qte_forme || 67}">

                            <!-- Conversion display -->
                            <div class="conversion-display mt-1 small text-info" style="display: none;">
                                <i class="fas fa-calculator"></i>
                                <span class="conversion-text">0 tonnes = 0 kg = 0 sacs</span>
                            </div>

                            <!-- Stock validation message -->
                            <div class="stock-validation-message mt-1 small text-danger" style="display: none;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span class="validation-text"></span>
                            </div>
                        </div>
                        <button type="button" class="btn btn-success btn-select-lot w-100" disabled>
                            <i class="fas fa-plus"></i> Ajouter à la vente
                        </button>
                    </div>
                </div>
            `;
        });
    } else {
        lotsHtml = '<div class="text-center text-muted py-3"><i class="fas fa-exclamation-triangle"></i> Aucun stock disponible</div>';
    }

    return `
        <div class="col-md-6 col-lg-4">
            <div class="product-card" data-produit-id="${produit.id}">
                <div class="product-header">
                    <div class="product-title">${produit.nom}</div>
                    <div class="product-subtitle">
                        <span class="badge bg-primary me-1">${produit.type}</span>
                        <span class="badge bg-info me-1">${produit.certification}</span>
                        <span class="badge ${stockStatus}">${stockText}</span>
                    </div>
                </div>
                <div class="product-body">
                    <div class="row mb-2">
                        <div class="col-6">
                            <small class="text-muted">Catégorie:</small><br>
                            <small>${produit.categorie_nom}</small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Prix de vente:</small><br>
                            <small class="fw-bold">${parseFloat(produit.prix_vente).toLocaleString()} Ar</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Stock total: <strong>${stockTotal} ${produit.unite_stock_nom}</strong></small>
                    </div>
                    <div class="lots-container">
                        <h6 class="mb-2">Lots disponibles:</h6>
                        ${lotsHtml}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function setupProductCardEvents() {
    // Événement de clic sur un lot
    $(document).off('click', '.lot-item').on('click', '.lot-item', function(e) {
        e.stopPropagation();

        // Désélectionner tous les autres lots
        $('.lot-item').removeClass('selected');
        $('.quantity-selector').removeClass('show');

        // Sélectionner ce lot
        $(this).addClass('selected');
        $(this).find('.quantity-selector').addClass('show');

        // Focus sur l'input de quantité
        $(this).find('.quantity-input').focus();
    });

    // Événement de saisie de quantité avec validation en temps réel
    $(document).off('input', '.quantity-input').on('input', '.quantity-input', function() {
        const input = $(this);
        const lotItem = input.closest('.lot-item');
        const quantite = parseFloat(input.val()) || 0;
        const maxStock = parseFloat(input.data('max-stock')) || 0;

        // Mise à jour de la conversion
        updateQuantityConversion(lotItem, quantite);

        // Validation du stock
        validateQuantityStock(lotItem, quantite, maxStock);
    });

    // Événement Enter pour ajouter rapidement
    $(document).off('keypress', '.quantity-input').on('keypress', '.quantity-input', function(e) {
        if (e.which === 13) { // Enter key
            const addButton = $(this).closest('.lot-item').find('.btn-select-lot');
            if (!addButton.prop('disabled')) {
                addButton.click();
            }
        }
    });

    // Événement de clic sur le bouton "Ajouter"
    $(document).off('click', '.btn-select-lot').on('click', '.btn-select-lot', function(e) {
        e.stopPropagation();

        const lotItem = $(this).closest('.lot-item');
        const produitId = lotItem.data('produit-id');
        const lotId = lotItem.data('lot-id');
        const quantite = parseFloat(lotItem.find('.quantity-input').val());

        if (!quantite || quantite <= 0) {
            showAlert('Erreur', 'Veuillez saisir une quantité valide', 'warning');
            return;
        }

        addProductToSale(produitId, lotId, quantite);
    });

    // Événements de filtrage
    $('#searchProduit, #filterType, #filterCertification, #filterDepot, #filterStock').off('input change').on('input change', function() {
        filterProducts();
    });

    $('#btnResetFilters').off('click').on('click', function() {
        resetProductFilters();
        filterProducts();
    });
}

// ===== FONCTIONS DE CONVERSION ET VALIDATION =====

function updateQuantityConversion(lotItem, quantite) {
    const conversionDisplay = lotItem.find('.conversion-display');
    const conversionText = lotItem.find('.conversion-text');

    if (quantite > 0) {
        const produitId = lotItem.data('produit-id');

        // Récupérer les informations du produit pour la conversion (même processus que gestion_achats.js)
        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'produits',
            sql: `SELECT
                    p.unite_stock_id,
                    u.libelle as unite_stock_libelle,
                    pr.libelle as presentation_libelle,
                    p.qte_presentation,
                    f.libelle as forme_libelle,
                    p.qte_forme
                  FROM produits p
                  LEFT JOIN unites u ON p.unite_stock_id = u.id
                  LEFT JOIN presentation pr ON p.presentation_id = pr.id
                  LEFT JOIN forme f ON p.forme_id = f.id
                  WHERE p.id = ?`,
            params: JSON.stringify([produitId])
        }).done(function(produitResponse) {
            if (produitResponse.success && produitResponse.data.length > 0) {
                const produit = produitResponse.data[0];

                // Convertir les tonnes vers l'unité de stock du produit
                const quantiteStock = convertTonnesToStock(
                    quantite,
                    produit.unite_stock_libelle,
                    produit.qte_forme || 67
                );

                // Formater l'affichage avec la quantité dans l'unité de stock
                let conversionInfo = formatStockQuantity(
                    quantiteStock,
                    produit.unite_stock_libelle,
                    produit.presentation_libelle,
                    produit.qte_presentation || 1,
                    produit.forme_libelle,
                    produit.qte_forme || 67
                );

                conversionText.text(`${quantite.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes = ${conversionInfo}`);
                conversionDisplay.show();
            } else {
                // Fallback si pas d'informations produit - conversion simple vers kg et sacs
                const kg = quantite * 1000;
                const sacs = Math.floor(kg / 67);
                const resteKg = kg % 67;
                let conversionTextStr = `${kg.toLocaleString('fr-FR')} kg`;
                if (sacs > 0) {
                    conversionTextStr += ` (${sacs} sac`;
                    if (sacs > 1) conversionTextStr += 's';
                    if (resteKg > 0) {
                        conversionTextStr += ` + ${resteKg.toFixed(2)} kg`;
                    }
                    conversionTextStr += ')';
                }
                conversionText.text(`${quantite.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes = ${conversionTextStr}`);
                conversionDisplay.show();
            }
        }).fail(function() {
            // Fallback en cas d'erreur - conversion simple vers kg et sacs
            const kg = quantite * 1000;
            const sacs = Math.floor(kg / 67);
            const resteKg = kg % 67;
            let conversionTextStr = `${kg.toLocaleString('fr-FR')} kg`;
            if (sacs > 0) {
                conversionTextStr += ` (${sacs} sac`;
                if (sacs > 1) conversionTextStr += 's';
                if (resteKg > 0) {
                    conversionTextStr += ` + ${resteKg.toFixed(2)} kg`;
                }
                conversionTextStr += ')';
            }
            conversionText.text(`${quantite.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes = ${conversionTextStr}`);
            conversionDisplay.show();
        });
    } else {
        conversionDisplay.hide();
    }
}

function validateQuantityStock(lotItem, quantite, maxStock) {
    const validationMessage = lotItem.find('.stock-validation-message');
    const validationText = lotItem.find('.validation-text');
    const addButton = lotItem.find('.btn-select-lot');
    const quantityInput = lotItem.find('.quantity-input');

    // Reset styles
    quantityInput.removeClass('is-invalid');
    validationMessage.hide();

    if (quantite <= 0) {
        // Quantité invalide
        addButton.prop('disabled', true);
        return false;
    }

    // Validation stricte : la quantité saisie (en tonnes) ne doit pas dépasser le stock disponible (déjà converti en tonnes)
    if (quantite > maxStock) {
        // Stock insuffisant - maxStock est déjà en tonnes grâce à la conversion dans loadProduits()
        quantityInput.addClass('is-invalid');
        validationText.text(`Stock insuffisant. Maximum disponible: ${maxStock.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes`);
        validationMessage.show();
        addButton.prop('disabled', true);
        return false;
    }

    // Quantité valide
    addButton.prop('disabled', false);
    return true;
}

// ===== FONCTIONS DE CONVERSION DE STOCK (même logique que gestion_achats.js) =====

/**
 * Convertit une quantité de stock vers les tonnes
 * @param {number} quantiteStock - Quantité dans l'unité de stock
 * @param {string} uniteStock - Unité de stock du produit (KG, SAC, TONNE, etc.)
 * @param {number} qteForme - Quantité de forme (kg par sac) pour les conversions SAC
 * @returns {number} Quantité en tonnes
 */
function convertStockToTonnes(quantiteStock, uniteStock, qteForme = 67) {
    if (!quantiteStock || quantiteStock === 0) return 0;

    // Si l'unité de stock est déjà en TONNE, pas de conversion
    if (uniteStock && uniteStock.toLowerCase().includes('tonne')) {
        return quantiteStock;
    }

    // Si l'unité de stock est en KG, convertir vers tonnes
    if (uniteStock && uniteStock.toLowerCase().includes('kg')) {
        return quantiteStock / 1000;
    }

    // Si l'unité de stock est en SAC, convertir sacs -> kg -> tonnes
    if (uniteStock && uniteStock.toLowerCase().includes('sac')) {
        const kgParSac = qteForme || 67;
        const totalKg = quantiteStock * kgParSac;
        return totalKg / 1000;
    }

    // Par défaut, considérer comme kg
    return quantiteStock / 1000;
}

/**
 * Convertit une quantité en tonnes vers l'unité de stock
 * @param {number} quantiteTonnes - Quantité en tonnes
 * @param {string} uniteStock - Unité de stock du produit (KG, SAC, TONNE, etc.)
 * @param {number} qteForme - Quantité de forme (kg par sac) pour les conversions SAC
 * @returns {number} Quantité dans l'unité de stock
 */
function convertTonnesToStock(quantiteTonnes, uniteStock, qteForme = 67) {
    if (!quantiteTonnes || quantiteTonnes === 0) return 0;

    // Si l'unité de stock est déjà en TONNE, pas de conversion
    if (uniteStock && uniteStock.toLowerCase().includes('tonne')) {
        return quantiteTonnes;
    }

    // Convertir d'abord en kg
    const quantiteKg = quantiteTonnes * 1000;

    // Si l'unité de stock est en KG, retourner les kg
    if (uniteStock && uniteStock.toLowerCase().includes('kg')) {
        return quantiteKg;
    }

    // Si l'unité de stock est en SAC, convertir kg -> sacs
    if (uniteStock && uniteStock.toLowerCase().includes('sac')) {
        const kgParSac = qteForme || 67;
        return quantiteKg / kgParSac;
    }

    // Par défaut, retourner les kg
    return quantiteKg;
}

/**
 * Formate l'affichage des quantités avec conversion selon la règle du produit
 * @param {number} stockKg - Stock en kg
 * @param {string} uniteStock - Unité de stock du produit
 * @param {string} presentation - Présentation du produit (ex: SAC)
 * @param {number} qtePresentation - Quantité de présentation (ex: 1)
 * @param {string} forme - Forme du produit (ex: KG)
 * @param {number} qteForme - Quantité de forme (ex: 67)
 * @returns {string} Affichage formaté
 */
function formatStockQuantity(stockKg, uniteStock, presentation = null, qtePresentation = 1, forme = null, qteForme = 67) {
    if (!stockKg || stockKg === 0) return `0 ${uniteStock || 'kg'}`;

    // Si l'unité de stock est KG et qu'on a des informations de présentation/forme
    if (uniteStock && uniteStock.toLowerCase().includes('kg') && presentation && forme) {
        // Appliquer la conversion selon la règle : 1 sac = qteForme kg
        const kgParSac = qteForme || 67; // Quantité de forme (kg par sac)
        const sacs = Math.floor(stockKg / kgParSac);
        const resteKg = stockKg % kgParSac;

        let display = `${stockKg} kg`;
        if (sacs > 0) {
            display += ` (${sacs} sac`;
            if (sacs > 1) display += 's';
            if (resteKg > 0) {
                display += ` + ${resteKg.toFixed(2)} kg`;
            }
            display += ')';
        }
        return display;
    }

    // Si l'unité de stock est SAC, convertir en kg
    if (uniteStock && uniteStock.toLowerCase().includes('sac')) {
        const kgParSac = qteForme || 67;
        const totalKg = stockKg * kgParSac;
        return `${stockKg} sac (${totalKg} kg)`;
    }

    return `${stockKg} ${uniteStock || 'kg'}`;
}

function addProductToSale(produitId, lotId, quantite) {
    const produit = window.produitsCache[produitId];
    const lot = produit.lots.find(l => l.stock_id == lotId);

    if (!lot) {
        showAlert('Erreur', 'Lot introuvable', 'error');
        return;
    }

    // Validation stricte : la quantité saisie (en tonnes) ne doit pas dépasser le stock disponible (déjà converti en tonnes)
    if (quantite > lot.quantite) {
        showAlert('Erreur',
            `Quantité demandée (${quantite.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes) supérieure au stock disponible (${lot.quantite.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes)`,
            'warning');
        return;
    }

    // Ajouter le produit au tableau de vente
    addProduitVenteFromSelection(produit, lot, quantite);

    // Fermer la section de sélection
    hideProductSelectionSection();

    showAlert('Succès', `${produit.nom} ajouté à la vente (${quantite.toLocaleString('fr-FR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} tonnes)`, 'success');
}

function addProduitVenteFromSelection(produit, lot, quantite) {
    const rowId = 'row_' + Date.now();

    // Créer une nouvelle ligne dans le tableau avec la structure originale
    const newRow = `
        <tr id="${rowId}">
            <td>
                <select class="form-select form-select-sm produit-select" required>
                    <option value="${produit.id}" selected data-prix="${produit.prix_vente}">${produit.nom}</option>
                </select>
                <input type="hidden" class="produit-id" value="${produit.id}">
                <input type="hidden" class="lot-id" value="${lot.stock_id}">
                <small class="text-muted">${produit.type} - ${produit.certification}</small>
            </td>
            <td>
                <select class="form-select form-select-sm depot-select" required>
                    <option value="${lot.depot_id}" selected>${lot.depot_nom}</option>
                </select>
                <input type="hidden" class="depot-id" value="${lot.depot_id}">
            </td>
            <td>
                <select class="form-select form-select-sm grade">
                    <option value="">Sélectionner un grade</option>
                    <option value="Standard" ${produit.type === 'Standard' ? 'selected' : ''}>Standard</option>
                    <option value="Supérieur" ${produit.type === 'Superieur' ? 'selected' : ''}>Supérieur</option>
                    <option value="Premium">Premium</option>
                    <option value="Vanille">Vanille</option>
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm qualite">
                    <option value="">Sélectionner une qualité</option>
                    <option value="Bio" ${produit.certification === 'Bio' ? 'selected' : ''}>Bio</option>
                    <option value="Conventionnel" ${produit.certification === 'Conventionnel' ? 'selected' : ''}>Conventionnel</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm qte-tonnes"
                       step="0.001" min="0" max="${lot.quantite}" value="${quantite}"
                       placeholder="0.000">
                <small class="text-muted d-block">Max: ${lot.quantite} ${produit.unite_stock_nom}</small>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm nbre-lot" min="0" placeholder="Nb lots">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm bl" placeholder="BL">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm conteneur" placeholder="Conteneur">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm seal" placeholder="Seal">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm lots"
                       value="${lot.lot_numero || ''}" placeholder="Lots">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm expedition" placeholder="Expédition">
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger btn-remove-produit" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;

    $('#tableDetailsVente tbody').append(newRow);

    // Ajouter les événements pour cette ligne
    const $newRow = $(`#${rowId}`);

    // Événement pour supprimer la ligne
    $newRow.find('.btn-remove-produit').on('click', function() {
        $newRow.remove();
    });
}

function filterProducts() {
    const searchTerm = $('#searchProduit').val().toLowerCase();
    const typeFilter = $('#filterType').val();
    const certificationFilter = $('#filterCertification').val();
    const depotFilter = $('#filterDepot').val();
    const stockFilter = $('#filterStock').val();

    let visibleCount = 0;

    $('.product-card').each(function() {
        const card = $(this);
        const produitId = card.data('produit-id');
        const produit = window.produitsCache[produitId];

        if (!produit) return;

        let visible = true;

        // Filtre par nom
        if (searchTerm && !produit.nom.toLowerCase().includes(searchTerm)) {
            visible = false;
        }

        // Filtre par type
        if (typeFilter && produit.type !== typeFilter) {
            visible = false;
        }

        // Filtre par certification
        if (certificationFilter && produit.certification !== certificationFilter) {
            visible = false;
        }

        // Filtre par dépôt
        if (depotFilter) {
            const hasDepot = produit.lots.some(lot => lot.depot_id == depotFilter);
            if (!hasDepot) {
                visible = false;
            }
        }

        // Filtre par stock
        if (stockFilter) {
            const stockTotal = produit.lots.reduce((total, lot) => total + lot.quantite, 0);

            if (stockFilter === 'available' && stockTotal <= 0) {
                visible = false;
            } else if (stockFilter === 'low' && stockTotal > produit.stock_min) {
                visible = false;
            }
        }

        if (visible) {
            card.closest('.col-md-6').show();
            visibleCount++;
        } else {
            card.closest('.col-md-6').hide();
        }
    });

    // Afficher/masquer le message "aucun produit"
    if (visibleCount === 0) {
        $('#noProductsMessage').removeClass('d-none');
    } else {
        $('#noProductsMessage').addClass('d-none');
    }
}

function resetProductFilters() {
    $('#searchProduit').val('');
    $('#filterType').val('');
    $('#filterCertification').val('');
    $('#filterDepot').val('');
    $('#filterStock').val('');
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

// ===== VALIDATION DE STOCK =====

function validateStockQuantity(row) {
    const qteInput = row.find('.qte-tonnes');
    const quantiteDemandee = parseFloat(qteInput.val()) || 0;
    const lotId = row.find('.lot-id').val();
    const produitId = row.find('.produit-id').val();

    if (!lotId || !produitId) {
        return true; // Pas de validation si pas de lot sélectionné
    }

    // Trouver le lot dans le cache
    const produit = window.produitsCache[produitId];
    if (!produit) {
        return false;
    }

    const lot = produit.lots.find(l => l.stock_id == lotId);
    if (!lot) {
        return false;
    }

    // Vérifier si la quantité demandée dépasse le stock disponible
    if (quantiteDemandee > lot.quantite) {
        // Afficher un message d'erreur
        qteInput.addClass('is-invalid');

        // Ajouter ou mettre à jour le message d'erreur
        let errorMsg = row.find('.stock-error-msg');
        if (errorMsg.length === 0) {
            errorMsg = $('<small class="text-danger stock-error-msg"></small>');
            qteInput.after(errorMsg);
        }
        errorMsg.text(`Stock insuffisant. Disponible: ${lot.quantite} ${produit.unite_stock_nom}`);

        return false;
    } else {
        // Supprimer l'erreur si la quantité est valide
        qteInput.removeClass('is-invalid');
        row.find('.stock-error-msg').remove();
        return true;
    }
}

function validateAllStockQuantities() {
    let allValid = true;

    $('#tableDetailsVente tbody tr').each(function() {
        const row = $(this);
        if (!validateStockQuantity(row)) {
            allValid = false;
        }
    });

    return allValid;
}

function checkStockAvailability(produitId, lotId, quantite) {
    const produit = window.produitsCache[produitId];
    if (!produit) {
        return { valid: false, message: 'Produit introuvable' };
    }

    const lot = produit.lots.find(l => l.stock_id == lotId);
    if (!lot) {
        return { valid: false, message: 'Lot introuvable' };
    }

    if (quantite > lot.quantite) {
        return {
            valid: false,
            message: `Stock insuffisant. Disponible: ${lot.quantite} ${produit.unite_stock_nom}`
        };
    }

    return { valid: true, message: 'Stock suffisant' };
}

function updateStockDisplay() {
    // Mettre à jour l'affichage du stock dans le modal de sélection
    $('.lot-item').each(function() {
        const lotItem = $(this);
        const produitId = lotItem.data('produit-id');
        const lotId = lotItem.data('lot-id');

        const produit = window.produitsCache[produitId];
        if (produit) {
            const lot = produit.lots.find(l => l.stock_id == lotId);
            if (lot) {
                const stockBadge = lotItem.find('.stock-badge');
                stockBadge.text(`${lot.quantite} ${produit.unite_stock_nom}`);

                // Mettre à jour la classe de couleur selon le stock
                stockBadge.removeClass('stock-high stock-medium stock-low');
                if (lot.quantite <= 10) {
                    stockBadge.addClass('stock-low');
                } else if (lot.quantite <= 50) {
                    stockBadge.addClass('stock-medium');
                } else {
                    stockBadge.addClass('stock-high');
                }

                // Mettre à jour le max de l'input
                lotItem.find('.quantity-input').attr('max', lot.quantite);
            }
        }
    });
}

// ===== ANCIENNE FONCTION ADDPRODUITVENTE (REDIRIGE VERS LA NOUVELLE INTERFACE) =====

function addProduitVente() {
    // Si on est en mode édition (modal ouvert), créer une ligne vide
    if ($('#modalVente').hasClass('show')) {
        addEmptyProductRow();
    } else {
        // Sinon, rediriger vers la nouvelle interface de sélection
        showProductSelectionSection();
    }
}

function addEmptyProductRow() {
    if (!window.produitsCache) {
        showAlert('Erreur', 'Les produits ne sont pas encore chargés', 'error');
        return;
    }

    const rowId = 'row_' + Date.now();
    let produitOptions = '<option value="">Sélectionner un produit</option>';
    let depotOptions = '<option value="">Sélectionner un dépôt</option>';

    Object.values(window.produitsCache).forEach(produit => {
        produitOptions += `<option value="${produit.id}" data-prix="${produit.prix_vente || 0}">${produit.nom}</option>`;
    });

    // Charger les dépôts
    if (window.depotsCache) {
        Object.values(window.depotsCache).forEach(depot => {
            depotOptions += `<option value="${depot.id}">${depot.libelle}</option>`;
        });
    }

    const newRow = `
        <tr id="${rowId}">
            <td>
                <select class="form-select form-select-sm produit-select" required>
                    ${produitOptions}
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm depot-select" required>
                    ${depotOptions}
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm grade">
                    <option value="">Sélectionner un grade</option>
                    <option value="Standard">Standard</option>
                    <option value="Supérieur">Supérieur</option>
                    <option value="Premium">Premium</option>
                    <option value="Vanille">Vanille</option>
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm qualite">
                    <option value="">Sélectionner une qualité</option>
                    <option value="Bio">Bio</option>
                    <option value="Conventionnel">Conventionnel</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm qte-tonnes" step="0.001" min="0" placeholder="0.000">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm nbre-lot" min="0" placeholder="Nb lots">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm bl" placeholder="BL">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm conteneur" placeholder="Conteneur">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm seal" placeholder="Seal">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm lots" placeholder="Lots">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm expedition" placeholder="Expédition">
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger btn-remove-produit" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;

    $('#tableDetailsVente tbody').append(newRow);

    // Événement pour supprimer la ligne
    $(`#${rowId} .btn-remove-produit`).on('click', function() {
        $(`#${rowId}`).remove();
    });
}

function updateRowCalculations(row) {
    const qte = parseFloat(row.find('.qte-tonnes').val()) || 0;
    const prix = parseFloat(row.find('.prix-unitaire').val()) || 0;
    const montant = qte * prix;

    // Mettre à jour l'affichage du montant
    const montantElement = row.find('.montant-ligne');
    if (montantElement.length > 0) {
        montantElement.text(montant.toLocaleString() + ' Ar');
    }
}

function updateTotals() {
    let totalHT = 0;

    $('#tableDetailsVente tbody tr').each(function() {
        const qte = parseFloat($(this).find('.qte-tonnes').val()) || 0;
        const prix = parseFloat($(this).find('.prix-unitaire').val()) || 0;
        totalHT += qte * prix;
    });

    const remise = 0; // Pour l'instant, pas de remise
    const totalTTC = totalHT - remise;

    $('#montantHT').text(totalHT.toLocaleString() + ' Ar');
    $('#montantRemise').text(remise.toLocaleString() + ' Ar');
    $('#montantTTC').text(totalTTC.toLocaleString() + ' Ar');

    updateCurrencyConversion();
}

function updateCurrencyConversion() {
    const totalAr = parseFloat($('#montantTTC').text().replace(/[^\d.-]/g, '')) || 0;
    const cours = parseFloat($('#coursDevise').val()) || 0; // Cours par défaut
    const totalEur = totalAr / cours;

    $('#valeurEUR').text(totalEur.toFixed(2) + ' €');
    $('#valeurAR').text(totalAr.toLocaleString() + ' Ar');
}

// ===== OPÉRATIONS CRUD =====

function saveVente() {
    // Validation du formulaire
    if (!validateVenteForm()) {
        return;
    }

    const venteData = collectVenteData();

    if (currentVenteId) {
        // Modification
        updateVente(currentVenteId, venteData);
    } else {
        // Création
        createVente(venteData);
    }
}

function validateVenteForm() {
    const clientId = $('#clientVente').val();
    if (!clientId) {
        showAlert('Erreur', 'Veuillez sélectionner un client', 'error');
        return false;
    }

    const rows = $('#tableDetailsVente tbody tr');
    if (rows.length === 0) {
        showAlert('Erreur', 'Veuillez ajouter au moins un produit', 'error');
        return false;
    }

    // Vérifier que tous les produits ont au minimum un produit et une quantité
    let isValid = true;
    let hasStockError = false;

    rows.each(function() {
        const row = $(this);
        const produitId = row.find('.produit-id').val() || row.find('.produit-select').val();
        const qte = parseFloat(row.find('.qte-tonnes').val()) || 0;

        if (!produitId || qte <= 0) {
            isValid = false;
            return false;
        }

        // Validation du stock
        if (!validateStockQuantity(row)) {
            hasStockError = true;
            isValid = false;
        }
    });

    if (!isValid) {
        if (hasStockError) {
            showAlert('Erreur', 'Certaines quantités dépassent le stock disponible. Veuillez corriger les erreurs.', 'error');
        } else {
            showAlert('Erreur', 'Veuillez remplir tous les champs des produits (produit et quantité > 0)', 'error');
        }
        return false;
    }

    return true;
}

function collectVenteData() {
    const totalAr = parseFloat($('#montantTTC').text().replace(/[^\d.-]/g, '')) || 0;
    const totalRemise = parseFloat($('#montantRemise').text().replace(/[^\d.-]/g, '')) || 0;
    const cours = parseFloat($('#coursDevise').val()) || 4875.40;
    const totalEur = totalAr / cours;

    // Collecter TOUTES les données de l'en-tête selon la structure ventes_entete
    const venteData = {
        client_id: $('#clientVente').val(),
        n_domiciliation: $('#nDomiciliation').val() || null,
        total_montant: totalAr,
        total_remise: totalRemise,
        date_vente: $('#dateVente').val(),
        statut: 'EN COURS', // Utiliser les nouvelles valeurs enum de la DB: 'EN COURS','EN ATTENTE','FACTURE','PAYE','ANNULE'
        valeur_euro: totalEur,
        valeur_ar: totalAr,
        cours_devise: cours,
        dau_numero: $('#dauNumero').val() || null,
        dau_date: $('#dauDate').val() || null,
        facture_numero: $('#numeroFacture').val() || $('#referenceVente').val(),
        facture_date: $('#dateFacture').val(),
        lieux_exportation: $('#lieuExportation').val() || null,
        cree_par: 'system', // Utilisateur qui crée
        details: []
    };

    // Collecter TOUS les détails selon la structure ventes_details
    $('#tableDetailsVente tbody tr').each(function() {
        const row = $(this);
        const detail = {
            produit_id: row.find('.produit-id').val() || row.find('.produit-select').val(),
            depot_id: row.find('.depot-id').val() || row.find('.depot-select').val() || 1,
            lot_stock_id: row.find('.lot-id').val() || null, // ID du stock spécifique
            expedition: row.find('.expedition').val() || null,
            grade: row.find('.grade').val() || null,
            qualite: row.find('.qualite').val() || null,
            qte_tonnes: parseFloat(row.find('.qte-tonnes').val()) || 0,
            qte_dernier_stock: 0, // À calculer depuis le stock actuel
            nbre_lot: parseInt(row.find('.nbre-lot').val()) || null,
            bl: row.find('.bl').val() || null,
            conteneur: row.find('.conteneur').val() || null,
            seal: row.find('.seal').val() || null,
            lots: row.find('.lots').val() || null,
            cree_par: 'system' // Utilisateur qui crée
        };
        venteData.details.push(detail);
    });

    return venteData;
}

function createVente(venteData) {
    // Utiliser l'action 'create' comme dans le système d'achats
    $.post('../includes/traitement.php', {
        action: 'create',
        table: 'ventes_entete',
        data: JSON.stringify({
            client_id: venteData.client_id,
            n_domiciliation: venteData.n_domiciliation,
            total_montant: venteData.total_montant,
            total_remise: venteData.total_remise,
            date_vente: venteData.date_vente,
            statut: venteData.statut,
            valeur_euro: venteData.valeur_euro,
            valeur_ar: venteData.valeur_ar,
            cours_devise: venteData.cours_devise,
            dau_numero: venteData.dau_numero,
            dau_date: venteData.dau_date,
            facture_numero: venteData.facture_numero,
            facture_date: venteData.facture_date,
            lieux_exportation: venteData.lieux_exportation,
            cree_par: venteData.cree_par
        })
    }).done(function(response) {
        if (response.success) {
            // Récupérer l'ID de la vente créée
            const venteId = response.id;
            createVenteDetails(venteId, venteData.details);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de créer la vente', 'error');
    });
}

function updateVente(venteId, venteData) {
    // Utiliser l'action 'update' comme dans le système d'achats
    $.post('../includes/traitement.php', {
        action: 'update',
        table: 'ventes_entete',
        id: venteId,
        data: JSON.stringify({
            client_id: venteData.client_id,
            n_domiciliation: venteData.n_domiciliation,
            total_montant: venteData.total_montant,
            total_remise: venteData.total_remise,
            date_vente: venteData.date_vente,
            statut: venteData.statut,
            valeur_euro: venteData.valeur_euro,
            valeur_ar: venteData.valeur_ar,
            cours_devise: venteData.cours_devise,
            dau_numero: venteData.dau_numero,
            dau_date: venteData.dau_date,
            facture_numero: venteData.facture_numero,
            facture_date: venteData.facture_date,
            lieux_exportation: venteData.lieux_exportation,
            dernier_modif_par: 'system'
        })
    }).done(function(response) {
        if (response.success) {
            // D'abord récupérer les IDs des détails existants pour les supprimer
            $.post('../includes/traitement.php', {
                action: 'execute_sql',
                table: 'ventes_details',
                sql: 'SELECT id FROM ventes_details WHERE vente_id = ?',
                params: JSON.stringify([venteId])
            }).done(function(detailsResponse) {
                if (detailsResponse.success && detailsResponse.data.length > 0) {
                    // Supprimer chaque détail individuellement
                    let deletedCount = 0;
                    const totalToDelete = detailsResponse.data.length;

                    detailsResponse.data.forEach(detail => {
                        $.post('../includes/traitement.php', {
                            action: 'delete',
                            table: 'ventes_details',
                            id: detail.id
                        }).done(function() {
                            deletedCount++;
                            if (deletedCount === totalToDelete) {
                                // Tous les détails supprimés, recréer les nouveaux
                                createVenteDetails(venteId, venteData.details);

                                showAlert('Succès', 'Vente mise à jour avec succès', 'success');
                                $('#modalVente').modal('hide');
                                loadVentes();

                                // Réinitialiser
                                currentVenteId = null;
                                $('#formVente')[0].reset();
                                $('#tableDetailsVente tbody').empty();
                            }
                        });
                    });
                } else {
                    // Aucun détail existant, créer directement les nouveaux
                    createVenteDetails(venteId, venteData.details);

                    showAlert('Succès', 'Vente mise à jour avec succès', 'success');
                    $('#modalVente').modal('hide');
                    loadVentes();

                    // Réinitialiser
                    currentVenteId = null;
                    $('#formVente')[0].reset();
                    $('#tableDetailsVente tbody').empty();
                }
            });
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de mettre à jour la vente', 'error');
    });
}

function createVenteDetails(venteId, details) {
    let detailsCreated = 0;
    const totalDetails = details.length;

    details.forEach(detail => {
        // Récupérer le stock actuel pour qte_dernier_stock
        let stockQuery = `SELECT COALESCE(SUM(quantite), 0) as stock_actuel
                         FROM produits_stock
                         WHERE produit_id = ? AND depot_id = ?`;
        let stockParams = [detail.produit_id, detail.depot_id];

        // Si on a un lot spécifique, récupérer son stock
        if (detail.lot_stock_id) {
            stockQuery = `SELECT quantite as stock_actuel
                         FROM produits_stock
                         WHERE id = ?`;
            stockParams = [detail.lot_stock_id];
        }

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'produits_stock',
            sql: stockQuery,
            params: JSON.stringify(stockParams)
        }).done(function(stockResponse) {
            const stockActuel = stockResponse.success && stockResponse.data.length > 0
                ? parseFloat(stockResponse.data[0].stock_actuel)
                : 0;

            // Utiliser l'action 'create' comme dans le système d'achats
            $.post('../includes/traitement.php', {
                action: 'create',
                table: 'ventes_details',
                data: JSON.stringify({
                    vente_id: venteId,
                    produit_id: detail.produit_id,
                    depot_id: detail.depot_id,
                    expedition: detail.expedition,
                    grade: detail.grade,
                    qualite: detail.qualite,
                    qte_tonnes: detail.qte_tonnes,
                    qte_dernier_stock: stockActuel,
                    nbre_lot: detail.nbre_lot,
                    bl: detail.bl,
                    conteneur: detail.conteneur,
                    seal: detail.seal,
                    lots: detail.lots,
                    cree_par: detail.cree_par
                })
            }).done(function(insertResponse) {
                if (insertResponse.success) {
                    // Créer le mouvement de stock SORTIE et mettre à jour le stock spécifique
                    if (detail.lot_stock_id) {
                        updateSpecificLotStock(detail.lot_stock_id, detail.qte_tonnes, venteId);
                    } else {
                        createMouvementStockVente(detail, venteId);
                    }

                    detailsCreated++;
                    if (detailsCreated === totalDetails) {
                        showAlert('Succès', 'Vente créée avec succès', 'success');
                        loadVentes();
                        $('#modalVente').modal('hide');
                    }
                } else {
                    showAlert('Erreur', insertResponse.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Erreur lors de la création des détails', 'error');
            });
        }).fail(function() {
            showAlert('Erreur', 'Erreur lors de la récupération du stock', 'error');
        });
    });
}

function editVente(id) {
    currentVenteId = id;

    // Charger les données de la vente
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: `SELECT * FROM ventes_entete WHERE id = ?`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const vente = response.data[0];

            // Remplir le formulaire
            $('#clientVente').val(vente.client_id).trigger('change');
            $('#dateVente').val(vente.date_vente);
            $('#dateFacture').val(vente.facture_date);
            $('#numeroFacture').val(vente.facture_numero);
            $('#lieuExportation').val(vente.lieu_exportation);
            $('#coursDevise').val(vente.cours_devise);
            $('#referenceVente').val(vente.facture_numero);

            // Charger les détails
            loadVenteDetails(id);

            // Mettre à jour le titre du modal
            $('#modalVenteTitle').html('<i class="fas fa-edit"></i> Modifier la Vente');
            $('#btnSaveText').text('Mettre à jour la Vente');

            $('#modalVente').modal('show');
        } else {
            showAlert('Erreur', 'Vente introuvable', 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger la vente', 'error');
    });
}

function loadVenteDetails(venteId) {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_details',
        sql: `SELECT vd.*, p.nom as produit_nom, p.prix_vente, p.type, p.certification,
                     d.libelle as depot_nom
              FROM ventes_details vd
              LEFT JOIN produits p ON vd.produit_id = p.id
              LEFT JOIN depot d ON vd.depot_id = d.id
              WHERE vd.vente_id = ?`,
        params: JSON.stringify([venteId])
    }).done(function(response) {
        if (response.success) {
            $('#tableDetailsVente tbody').empty();

            response.data.forEach(detail => {
                // Créer une ligne directement avec les données existantes
                const rowId = 'row_' + Date.now() + '_' + detail.id;

                const newRow = `
                    <tr id="${rowId}">
                        <td>
                            <select class="form-select form-select-sm produit-select" required>
                                <option value="${detail.produit_id}" selected data-prix="${detail.prix_vente}">${detail.produit_nom}</option>
                            </select>
                            <input type="hidden" class="produit-id" value="${detail.produit_id}">
                            <small class="text-muted">${detail.type || ''} - ${detail.certification || ''}</small>
                        </td>
                        <td>
                            <select class="form-select form-select-sm depot-select" required>
                                <option value="${detail.depot_id}" selected>${detail.depot_nom || 'Dépôt'}</option>
                            </select>
                            <input type="hidden" class="depot-id" value="${detail.depot_id}">
                        </td>
                        <td>
                            <select class="form-select form-select-sm grade">
                                <option value="">Sélectionner un grade</option>
                                <option value="Standard" ${detail.grade === 'Standard' ? 'selected' : ''}>Standard</option>
                                <option value="Supérieur" ${detail.grade === 'Supérieur' ? 'selected' : ''}>Supérieur</option>
                                <option value="Premium" ${detail.grade === 'Premium' ? 'selected' : ''}>Premium</option>
                                <option value="Vanille" ${detail.grade === 'Vanille' ? 'selected' : ''}>Vanille</option>
                            </select>
                        </td>
                        <td>
                            <select class="form-select form-select-sm qualite">
                                <option value="">Sélectionner une qualité</option>
                                <option value="Bio" ${detail.qualite === 'Bio' ? 'selected' : ''}>Bio</option>
                                <option value="Conventionnel" ${detail.qualite === 'Conventionnel' ? 'selected' : ''}>Conventionnel</option>
                            </select>
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm qte-tonnes"
                                   step="0.001" min="0" value="${detail.qte_tonnes || 0}"
                                   placeholder="0.000">
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm nbre-lot"
                                   min="0" value="${detail.nbre_lot || ''}" placeholder="Nb lots">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm bl"
                                   value="${detail.bl || ''}" placeholder="BL">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm conteneur"
                                   value="${detail.conteneur || ''}" placeholder="Conteneur">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm seal"
                                   value="${detail.seal || ''}" placeholder="Seal">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm lots"
                                   value="${detail.lots || ''}" placeholder="Lots">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm expedition"
                                   value="${detail.expedition || ''}" placeholder="Expédition">
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-danger btn-remove-produit" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                $('#tableDetailsVente tbody').append(newRow);

                // Ajouter les événements pour cette ligne
                const $newRow = $(`#${rowId}`);

                // Événement pour supprimer la ligne
                $newRow.find('.btn-remove-produit').on('click', function() {
                    $newRow.remove();
                });
            });
        }
    });
}

// ===== GESTION DES MOUVEMENTS DE STOCK =====

function createMouvementStockVente(detail, venteId) {
    // Récupérer les informations du produit pour la conversion
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT
                p.unite_stock_id,
                u.libelle as unite_stock_libelle,
                pr.libelle as presentation_libelle,
                p.qte_presentation,
                f.libelle as forme_libelle,
                p.qte_forme
              FROM produits p
              LEFT JOIN unites u ON p.unite_stock_id = u.id
              LEFT JOIN presentation pr ON p.presentation_id = pr.id
              LEFT JOIN forme f ON p.forme_id = f.id
              WHERE p.id = ?`,
        params: JSON.stringify([detail.produit_id])
    }).done(function(produitResponse) {
        let quantiteStock, conversionInfo = '';

        if (produitResponse.success && produitResponse.data.length > 0) {
            const produit = produitResponse.data[0];

            // ✅ CORRECTION : Convertir la quantité vendue (tonnes) vers l'unité de stock du produit
            quantiteStock = convertTonnesToStock(
                detail.qte_tonnes,
                produit.unite_stock_libelle,
                produit.qte_forme || 67
            );

            // Formater l'affichage pour le mouvement de stock
            conversionInfo = formatStockQuantity(
                quantiteStock,
                produit.unite_stock_libelle,
                produit.presentation_libelle,
                produit.qte_presentation || 1,
                produit.forme_libelle,
                produit.qte_forme || 67
            );
        } else {
            // Fallback : convertir en kg par défaut
            quantiteStock = detail.qte_tonnes * 1000;
            conversionInfo = `${quantiteStock} kg`;
        }

        const mouvementData = {
            produit_id: detail.produit_id,
            depot_id: detail.depot_id || 1,
            type_mouvement: 'SORTIE', // SORTIE pour les ventes (inverse de ENTREE pour achats)
            quantite: quantiteStock, // ✅ CORRECTION : Quantité dans l'unité de stock
            unite_id: produitResponse.success && produitResponse.data.length > 0 ?
                     produitResponse.data[0].unite_stock_id : 1, // Utiliser l'unité du produit ou défaut
            lot_numero: detail.lots || `VTE-${venteId}`,
            reference_document: `VTE-${venteId}`,
            motif: `Vente ${venteId} - ${detail.qte_tonnes} tonnes (${conversionInfo})`,
            date_mouvement: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0]
        };

        console.log('Création mouvement de stock vente:', mouvementData);

        $.post('../includes/traitement.php', {
            action: 'create',
            table: 'mouvements_stock',
            data: JSON.stringify(mouvementData)
        }).done(function(response) {
            if (response.success) {
                console.log('Mouvement de stock vente créé avec succès');
            } else {
                console.error('Erreur lors de la création du mouvement de stock vente:', response.message);
            }
        }).fail(function(xhr, status, error) {
            console.error('Erreur AJAX lors de la création du mouvement de stock vente:', error);
        });
    }).fail(function() {
        console.error('Erreur lors de la récupération des informations produit pour le mouvement de stock');
    });
}

function updateStockAfterVente(produitId, depotId, quantiteTonnes) {
    // Récupérer le stock actuel avec les informations d'unité
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits_stock',
        sql: `SELECT ps.id, ps.quantite, p.unite_stock_id, u.libelle as unite_stock_libelle, p.qte_forme
              FROM produits_stock ps
              LEFT JOIN produits p ON ps.produit_id = p.id
              LEFT JOIN unites u ON p.unite_stock_id = u.id
              WHERE ps.produit_id = ? AND ps.depot_id = ?`,
        params: JSON.stringify([produitId, depotId])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const stock = response.data[0];

            // ✅ CORRECTION : Convertir la quantité vendue (tonnes) vers l'unité de stock du produit
            const quantiteStock = convertTonnesToStock(
                quantiteTonnes,
                stock.unite_stock_libelle,
                stock.qte_forme || 67
            );

            const nouvelleQuantite = Math.max(0, parseFloat(stock.quantite) - quantiteStock);

            // Mettre à jour le stock (cette partie utilise déjà la bonne action 'update')
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'produits_stock',
                id: stock.id,
                data: JSON.stringify({ quantite: nouvelleQuantite })
            }).done(function(updateResponse) {
                if (updateResponse.success) {
                    console.log(`Stock mis à jour: ${stock.quantite} -> ${nouvelleQuantite} ${stock.unite_stock_libelle || 'unités'}`);
                } else {
                    console.error('Erreur lors de la mise à jour du stock:', updateResponse.message);
                }
            });
        } else {
            console.warn('Stock non trouvé pour le produit', produitId, 'dans le dépôt', depotId);
        }
    });
}

function updateSpecificLotStock(lotStockId, quantiteVendue, venteId) {
    // Récupérer le stock actuel du lot spécifique avec les informations d'unité
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits_stock',
        sql: `SELECT ps.*, p.nom as produit_nom, d.libelle as depot_nom,
                     p.unite_stock_id, u.libelle as unite_stock_libelle,
                     p.qte_forme
              FROM produits_stock ps
              LEFT JOIN produits p ON ps.produit_id = p.id
              LEFT JOIN depot d ON ps.depot_id = d.id
              LEFT JOIN unites u ON p.unite_stock_id = u.id
              WHERE ps.id = ?`,
        params: JSON.stringify([lotStockId])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const stock = response.data[0];

            // ✅ CORRECTION : Convertir la quantité vendue (tonnes) vers l'unité de stock du produit
            const quantiteStock = convertTonnesToStock(
                quantiteVendue,
                stock.unite_stock_libelle,
                stock.qte_forme || 67
            );

            const nouvelleQuantite = Math.max(0, parseFloat(stock.quantite) - quantiteStock);

            // Mettre à jour le stock du lot spécifique
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'produits_stock',
                id: lotStockId,
                data: JSON.stringify({ quantite: nouvelleQuantite })
            }).done(function(updateResponse) {
                if (updateResponse.success) {
                    console.log(`Stock lot ${stock.lot_numero} mis à jour: ${stock.quantite} -> ${nouvelleQuantite} ${stock.unite_stock_libelle || 'unités'}`);

                    // Créer le mouvement de stock pour ce lot spécifique
                    createMouvementStockForLot(stock, quantiteStock, venteId);

                    // Mettre à jour le cache local
                    updateLocalStockCache(stock.produit_id, lotStockId, nouvelleQuantite);
                } else {
                    console.error('Erreur lors de la mise à jour du stock du lot:', updateResponse.message);
                }
            });
        } else {
            console.warn('Lot de stock non trouvé:', lotStockId);
        }
    });
}

function createMouvementStockForLot(stock, quantiteStock, venteId) {
    // Les informations d'unité sont déjà disponibles dans l'objet stock
    // quantiteStock est déjà dans l'unité de stock du produit

    // Formater l'affichage pour le mouvement de stock
    let conversionInfo = formatStockQuantity(
        quantiteStock,
        stock.unite_stock_libelle,
        null, // presentation
        1,    // qte_presentation
        null, // forme
        stock.qte_forme || 67
    );

    const mouvementData = {
        produit_id: stock.produit_id,
        depot_id: stock.depot_id,
        type_mouvement: 'SORTIE',
        quantite: quantiteStock, // ✅ CORRECTION : Quantité dans l'unité de stock
        unite_id: stock.unite_stock_id || 3, // Utiliser l'unité du produit ou défaut
        lot_numero: stock.lot_numero || `VTE-${venteId}`,
        reference_document: `VTE-${venteId}`,
        motif: `Vente ${venteId} - Lot ${stock.lot_numero} - ${conversionInfo}`,
        date_mouvement: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0]
    };

    $.post('../includes/traitement.php', {
        action: 'create',
        table: 'mouvements_stock',
        data: JSON.stringify(mouvementData)
    }).done(function(response) {
        if (response.success) {
            console.log('Mouvement de stock créé pour le lot:', stock.lot_numero);
        } else {
            console.error('Erreur lors de la création du mouvement de stock:', response.message);
        }
    });
}

function updateLocalStockCache(produitId, lotStockId, nouvelleQuantite) {
    // Mettre à jour le cache local pour refléter les changements
    if (window.produitsCache && window.produitsCache[produitId]) {
        const produit = window.produitsCache[produitId];
        const lot = produit.lots.find(l => l.stock_id == lotStockId);
        if (lot) {
            lot.quantite = nouvelleQuantite / 1000; // Reconvertir en tonnes pour l'affichage
        }
    }
}

// ===== WORKFLOW DES VENTES =====

function pendingVente(id) {
    showConfirm(
        'Mettre en attente',
        'Êtes-vous sûr de vouloir mettre cette vente en attente ?',
        'Oui, mettre en attente'
    ).then((result) => {
        if (result.isConfirmed) {
            updateVenteStatus(id, 'EN ATTENTE', 'Vente mise en attente avec succès');
        }
    });
}

function invoiceVente(id) {
    showConfirm(
        'Facturer la vente',
        'Êtes-vous sûr de vouloir facturer cette vente ?',
        'Oui, facturer'
    ).then((result) => {
        if (result.isConfirmed) {
            updateVenteStatus(id, 'FACTURE', 'Vente facturée avec succès');
        }
    });
}

function cancelVente(id) {
    showConfirm(
        'Annuler la vente',
        'Êtes-vous sûr de vouloir annuler cette vente ? Cette action est irréversible.',
        'Oui, annuler'
    ).then((result) => {
        if (result.isConfirmed) {
            updateVenteStatus(id, 'ANNULE', 'Vente annulée avec succès');
        }
    });
}

function deleteVente(id) {
    showConfirm(
        'Supprimer la vente',
        'Êtes-vous sûr de vouloir supprimer définitivement cette vente ? Le stock sera restauré automatiquement.',
        'Oui, supprimer'
    ).then((result) => {
        if (result.isConfirmed) {
            deleteVenteWithStockRestoration(id);
        }
    });
}

function deleteVenteWithStockRestoration(venteId) {
    // Étape 1 : Récupérer tous les détails de la vente pour restaurer le stock
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_details',
        sql: `SELECT
                vd.id,
                vd.produit_id,
                vd.depot_id,
                vd.qte_tonnes,
                vd.lots as lot_numero,
                p.unite_stock_id,
                p.qte_forme,
                u.libelle as unite_stock_nom
              FROM ventes_details vd
              LEFT JOIN produits p ON vd.produit_id = p.id
              LEFT JOIN unites u ON p.unite_stock_id = u.id
              WHERE vd.vente_id = ?`,
        params: JSON.stringify([venteId])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            // Traiter chaque détail pour restaurer le stock
            let processedDetails = 0;
            const totalDetails = response.data.length;

            response.data.forEach(detail => {
                restoreStockForDetail(detail, venteId, () => {
                    processedDetails++;
                    if (processedDetails === totalDetails) {
                        // Tous les stocks restaurés, supprimer la vente
                        finalizeVenteDeletion(venteId);
                    }
                });
            });
        } else {
            // Aucun détail trouvé, supprimer directement la vente
            finalizeVenteDeletion(venteId);
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de récupérer les détails de la vente', 'error');
    });
}

function restoreStockForDetail(detail, venteId, callback) {
    // Convertir la quantité vendue (en tonnes) vers l'unité de stock
    const quantiteStock = convertTonnesToStock(
        detail.qte_tonnes,
        detail.unite_stock_nom,
        detail.qte_forme || 67
    );

    // Trouver le lot correspondant dans produits_stock
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits_stock',
        sql: `SELECT id, quantite FROM produits_stock
              WHERE produit_id = ? AND depot_id = ? AND lot_numero = ?`,
        params: JSON.stringify([detail.produit_id, detail.depot_id, detail.lot_numero])
    }).done(function(stockResponse) {
        if (stockResponse.success && stockResponse.data.length > 0) {
            const stockRecord = stockResponse.data[0];
            const nouvelleQuantite = parseFloat(stockRecord.quantite) + quantiteStock;

            // Mettre à jour la quantité en stock
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'produits_stock',
                id: stockRecord.id,
                data: JSON.stringify({
                    quantite: nouvelleQuantite
                })
            }).done(function(updateResponse) {
                if (updateResponse.success) {
                    // Créer un mouvement de stock ENTREE pour traçabilité
                    createStockRestorationMovement(detail, venteId, quantiteStock, callback);
                } else {
                    console.error('Erreur lors de la mise à jour du stock:', updateResponse.message);
                    callback();
                }
            }).fail(function() {
                console.error('Erreur lors de la mise à jour du stock');
                callback();
            });
        } else {
            console.warn('Lot de stock non trouvé pour la restauration:', detail);
            callback();
        }
    }).fail(function() {
        console.error('Erreur lors de la recherche du lot de stock');
        callback();
    });
}

function createStockRestorationMovement(detail, venteId, quantiteStock, callback) {
    const mouvementData = {
        produit_id: detail.produit_id,
        depot_id: detail.depot_id,
        type_mouvement: 'ENTREE',
        quantite: quantiteStock,
        unite_id: detail.unite_stock_id || 1,
        lot_numero: detail.lot_numero || `VTE-${venteId}`,
        reference_document: `ANNUL-VTE-${venteId}`,
        motif: `Restauration stock - Suppression vente #${venteId}`,
        date_mouvement: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0]
    };

    $.post('../includes/traitement.php', {
        action: 'create',
        table: 'mouvements_stock',
        data: JSON.stringify(mouvementData)
    }).done(function(response) {
        if (response.success) {
            console.log('Mouvement de restauration créé pour le lot:', detail.lot_numero);
        } else {
            console.error('Erreur lors de la création du mouvement de restauration:', response.message);
        }
        callback();
    }).fail(function() {
        console.error('Erreur lors de la création du mouvement de restauration');
        callback();
    });
}

function finalizeVenteDeletion(venteId) {
    // Étape 1 : Supprimer tous les détails de vente
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_details',
        sql: 'SELECT id FROM ventes_details WHERE vente_id = ?',
        params: JSON.stringify([venteId])
    }).done(function(detailsResponse) {
        if (detailsResponse.success && detailsResponse.data.length > 0) {
            let deletedDetails = 0;
            const totalDetails = detailsResponse.data.length;

            detailsResponse.data.forEach(detail => {
                $.post('../includes/traitement.php', {
                    action: 'delete',
                    table: 'ventes_details',
                    id: detail.id
                }).done(function() {
                    deletedDetails++;
                    if (deletedDetails === totalDetails) {
                        // Tous les détails supprimés, supprimer l'en-tête
                        deleteVenteHeader(venteId);
                    }
                });
            });
        } else {
            // Aucun détail, supprimer directement l'en-tête
            deleteVenteHeader(venteId);
        }
    });
}

function deleteVenteHeader(venteId) {
    $.post('../includes/traitement.php', {
        action: 'delete',
        table: 'ventes_entete',
        id: venteId
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', 'Vente supprimée avec succès. Le stock a été restauré.', 'success');
            loadVentes();
        } else {
            showAlert('Erreur', 'Erreur lors de la suppression de la vente: ' + response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de supprimer la vente', 'error');
    });
}

function updateVenteStatus(id, newStatus, successMessage) {
    // Utiliser l'action 'update' comme dans le système d'achats
    $.post('../includes/traitement.php', {
        action: 'update',
        table: 'ventes_entete',
        id: id,
        data: JSON.stringify({
            statut: newStatus,
            dernier_modif_par: 'system'
        })
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', successMessage, 'success');
            loadVentes();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de mettre à jour le statut', 'error');
    });
}

// ===== GESTION DES PAIEMENTS SUPPRIMÉE =====
// Utiliser la page gestion_paiements_ventes.php pour les paiements

// ===== FILTRES =====

function applyFilters() {
    const status = $('#filterStatus').val();
    const dateFrom = $('#filterDateFrom').val();
    const dateTo = $('#filterDateTo').val();
    const clientId = $('#filterClient').val();

    let sql = `SELECT
                ve.id,
                ve.facture_numero as reference_vente,
                c.nom as client_nom,
                ve.date_vente,
                ve.facture_date,
                ve.statut,
                ve.total_montant,
                ve.valeur_euro,
                ve.valeur_ar
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              WHERE 1=1`;

    const params = [];

    if (status) {
        sql += ` AND ve.statut = ?`;
        params.push(status);
    }

    if (dateFrom) {
        sql += ` AND ve.date_vente >= ?`;
        params.push(dateFrom);
    }

    if (dateTo) {
        sql += ` AND ve.date_vente <= ?`;
        params.push(dateTo);
    }

    if (clientId) {
        sql += ` AND ve.client_id = ?`;
        params.push(clientId);
    }

    sql += ` ORDER BY ve.date_creation DESC`;

    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: sql,
        params: JSON.stringify(params)
    }).done(function(response) {
        if (response.success) {
            displayVentesFiltered(response.data);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible d\'appliquer les filtres', 'error');
    });
}

function resetFilters() {
    $('#filterStatus').val('');
    $('#filterDateFrom').val('');
    $('#filterDateTo').val('');
    $('#filterClient').val('').trigger('change');
    loadVentes();
}

// ===== AUTRES FONCTIONS =====

function viewVente(id) {
    // Charger les données de la vente avec TOUS les détails
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: `SELECT
                ve.*,
                c.nom as client_nom,
                c.adresse as client_adresse,
                c.telephone as client_telephone,
                c.email as client_email,
                c.type_client
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              WHERE ve.id = ?`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const vente = response.data[0];

            // Charger les détails de la vente avec TOUTES les colonnes
            $.post('../includes/traitement.php', {
                action: 'execute_sql',
                table: 'ventes_details',
                sql: `SELECT
                        vd.*,
                        p.nom as produit_nom,
                        d.libelle as depot_nom
                      FROM ventes_details vd
                      LEFT JOIN produits p ON vd.produit_id = p.id
                      LEFT JOIN depot d ON vd.depot_id = d.id
                      WHERE vd.vente_id = ?
                      ORDER BY vd.id`,
                params: JSON.stringify([id])
            }).done(function(detailsResponse) {
                if (detailsResponse.success) {
                    displayVenteDetails(vente, detailsResponse.data);
                } else {
                    showAlert('Erreur', 'Impossible de charger les détails', 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de charger les détails', 'error');
            });
        } else {
            showAlert('Erreur', 'Vente non trouvée', 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger la vente', 'error');
    });
}

function displayVenteDetails(vente, details) {
    const statutBadge = getStatutBadge(vente.statut);
    const montantTotal = details.reduce((sum, detail) => sum + parseFloat(detail.qte_tonnes || 0) * parseFloat(detail.prix_unitaire || 0), 0);

    let content = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Informations Générales</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Référence Facture:</strong></td><td>${vente.facture_numero || 'N/A'}</td></tr>
                            <tr><td><strong>Client:</strong></td><td>${vente.client_nom || 'N/A'}</td></tr>
                            <tr><td><strong>Type Client:</strong></td><td>${vente.type_client || 'N/A'}</td></tr>
                            <tr><td><strong>Date Vente:</strong></td><td>${vente.date_vente || 'N/A'}</td></tr>
                            <tr><td><strong>Date Facture:</strong></td><td>${vente.facture_date || 'N/A'}</td></tr>
                            <tr><td><strong>Statut:</strong></td><td>${statutBadge}</td></tr>
                            <tr><td><strong>N° Domiciliation:</strong></td><td>${vente.n_domiciliation || 'N/A'}</td></tr>
                            <tr><td><strong>DAU N°:</strong></td><td>${vente.dau_numero || 'N/A'}</td></tr>
                            <tr><td><strong>DAU Date:</strong></td><td>${vente.dau_date || 'N/A'}</td></tr>
                            <tr><td><strong>Lieu Exportation:</strong></td><td>${vente.lieux_exportation || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Informations Financières</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Total Montant:</strong></td><td>${parseFloat(vente.total_montant || 0).toLocaleString()} Ar</td></tr>
                            <tr><td><strong>Total Remise:</strong></td><td>${parseFloat(vente.total_remise || 0).toLocaleString()} Ar</td></tr>
                            <tr><td><strong>Valeur EUR:</strong></td><td>${parseFloat(vente.valeur_euro || 0).toFixed(2)} €</td></tr>
                            <tr><td><strong>Valeur AR:</strong></td><td>${parseFloat(vente.valeur_ar || 0).toLocaleString()} Ar</td></tr>
                            <tr><td><strong>Cours Devise:</strong></td><td>${parseFloat(vente.cours_devise || 0).toFixed(4)}</td></tr>
                            <tr><td><strong>Créé le:</strong></td><td>${vente.date_creation || 'N/A'}</td></tr>
                            <tr><td><strong>Créé par:</strong></td><td>${vente.cree_par || 'N/A'}</td></tr>
                            <tr><td><strong>Dernière modif:</strong></td><td>${vente.date_derniere_modif || 'N/A'}</td></tr>
                            <tr><td><strong>Modifié par:</strong></td><td>${vente.dernier_modif_par || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Informations Client</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Nom:</strong></td><td>${vente.client_nom || 'N/A'}</td></tr>
                            <tr><td><strong>Adresse:</strong></td><td>${vente.client_adresse || 'N/A'}</td></tr>
                            <tr><td><strong>Téléphone:</strong></td><td>${vente.client_telephone || 'N/A'}</td></tr>
                            <tr><td><strong>Email:</strong></td><td>${vente.client_email || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Résumé</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Nombre de produits:</strong></td><td>${details.length}</td></tr>
                            <tr><td><strong>Quantité totale:</strong></td><td>${details.reduce((sum, d) => sum + parseFloat(d.qte_tonnes || 0), 0).toFixed(3)} T</td></tr>
                            <tr><td><strong>Nombre de lots:</strong></td><td>${details.reduce((sum, d) => sum + parseInt(d.nbre_lot || 0), 0)}</td></tr>
                        </table>

                        ${vente.statut === 'FACTURE' ? `
                        <div class="mt-3">
                            <button class="btn btn-success btn-sm" onclick="window.open('gestion_paiements_ventes.php', '_blank')" title="Aller à la gestion des paiements">
                                <i class="fas fa-credit-card"></i> Gérer les Paiements
                            </button>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Détails des Produits</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Produit</th>
                                        <th>Dépôt</th>
                                        <th>Grade</th>
                                        <th>Qualité</th>
                                        <th>Qté (T)</th>
                                        <th>Stock Dernier</th>
                                        <th>Nb Lots</th>
                                        <th>BL</th>
                                        <th>Conteneur</th>
                                        <th>Seal</th>
                                        <th>Lots</th>
                                        <th>Expédition</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    details.forEach(detail => {
        content += `
            <tr>
                <td>${detail.produit_nom || 'N/A'}</td>
                <td>${detail.depot_nom || 'N/A'}</td>
                <td>${detail.grade || 'N/A'}</td>
                <td>${detail.qualite || 'N/A'}</td>
                <td>${parseFloat(detail.qte_tonnes || 0).toFixed(3)}</td>
                <td>${parseFloat(detail.qte_dernier_stock || 0).toFixed(3)}</td>
                <td>${detail.nbre_lot || 0}</td>
                <td>${detail.bl || 'N/A'}</td>
                <td>${detail.conteneur || 'N/A'}</td>
                <td>${detail.seal || 'N/A'}</td>
                <td>${detail.lots || 'N/A'}</td>
                <td>${detail.expedition || 'N/A'}</td>
            </tr>
        `;
    });

    content += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    `;

    $('#detailsContent').html(content);
    $('#modalDetailsVente').modal('show');
}

function deleteVente(id) {
    showConfirm(
        'Supprimer la vente',
        'Êtes-vous sûr de vouloir supprimer cette vente ? Cette action est irréversible.',
        'Oui, supprimer'
    ).then((result) => {
        if (result.isConfirmed) {
            // Utiliser l'action 'delete' pour supprimer l'en-tête (les détails seront supprimés par CASCADE)
            $.post('../includes/traitement.php', {
                action: 'delete',
                table: 'ventes_entete',
                id: id
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', 'Vente supprimée avec succès', 'success');
                    loadVentes();
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de supprimer la vente', 'error');
            });
        }
    });
}

function cancelVentes(ids) {
    showConfirm(
        'Annuler les ventes',
        `Êtes-vous sûr de vouloir annuler ${ids.length} vente(s) sélectionnée(s) ?`,
        'Oui, annuler'
    ).then((result) => {
        if (result.isConfirmed) {
            // Mettre à jour chaque vente individuellement comme dans le système d'achats
            let completed = 0;
            ids.forEach(id => {
                $.post('../includes/traitement.php', {
                    action: 'update',
                    table: 'ventes_entete',
                    id: id,
                    data: JSON.stringify({
                        statut: 'ANNULE',
                        dernier_modif_par: 'system'
                    })
                }).done(function(response) {
                    completed++;
                    if (completed === ids.length) {
                        showAlert('Succès', `${ids.length} vente(s) annulée(s) avec succès`, 'success');
                        loadVentes();
                        // Décocher toutes les cases
                        $('#tableVentes tbody input[type="checkbox"]').prop('checked', false);
                        $('#selectAll').prop('checked', false);
                    }
                }).fail(function() {
                    completed++;
                    if (completed === ids.length) {
                        showAlert('Erreur', 'Erreur lors de l\'annulation', 'error');
                    }
                });
            });
        }
    });
}

function sendEmailVente(id) {
    showAlert('Information', 'Fonctionnalité d\'envoi par email en cours de développement', 'info');
}

function printVente(id) {
    // Ouvrir une nouvelle fenêtre pour l'impression - même approche que printAchat
    const printUrl = `../includes/generate_vente_pdf.php?id=${id}`;
    window.open(printUrl, '_blank');
}

// ===== GESTION DU CHECKBOX "TOUT SÉLECTIONNER" =====

$(document).on('change', '#selectAll', function() {
    const isChecked = $(this).is(':checked');
    $('#tableVentes tbody input[type="checkbox"]').prop('checked', isChecked);
});

$(document).on('change', '#tableVentes tbody input[type="checkbox"]', function() {
    const totalCheckboxes = $('#tableVentes tbody input[type="checkbox"]').length;
    const checkedCheckboxes = $('#tableVentes tbody input[type="checkbox"]:checked').length;

    $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
});

// ===== INITIALISATION AU CHARGEMENT =====

$(document).ready(function() {
    // Toutes les initialisations sont déjà dans la fonction principale
    console.log('Gestion des ventes initialisée');
});
