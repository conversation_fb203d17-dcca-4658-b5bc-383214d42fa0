<?php
/**
 * Génération de PDF pour les ventes (factures de vente)
 * Similaire à generate_achat_pdf.php mais pour les ventes
 */

session_start();
require_once '../config/database.php';
require_once '../vendor/autoload.php';

// Vérification de l'authentification
if (!isset($_SESSION['username'])) {
    die('Accès non autorisé');
}

$venteId = $_GET['id'] ?? null;

if (!$venteId) {
    die('ID de vente manquant');
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Récupérer les données de la vente
    $sql = "SELECT 
                ve.*,
                c.nom as client_nom,
                c.adresse as client_adresse,
                c.telephone as client_telephone,
                c.email as client_email,
                c.nif as client_nif,
                c.stat as client_stat,
                c.type_client
            FROM ventes_entete ve
            LEFT JOIN clients c ON ve.client_id = c.id
            WHERE ve.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$venteId]);
    $vente = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$vente) {
        die('Vente non trouvée');
    }
    
    // Récupérer les détails de la vente
    $sqlDetails = "SELECT 
                    vd.*,
                    p.nom as produit_nom,
                    d.libelle as depot_libelle
                  FROM ventes_details vd
                  LEFT JOIN produits p ON vd.produit_id = p.id
                  LEFT JOIN depot d ON vd.depot_id = d.id
                  WHERE vd.vente_id = ?
                  ORDER BY vd.id";
    
    $stmtDetails = $db->prepare($sqlDetails);
    $stmtDetails->execute([$venteId]);
    $details = $stmtDetails->fetchAll(PDO::FETCH_ASSOC);
    
    // Créer le PDF avec marges optimisées pour une page
    $mpdf = new \Mpdf\Mpdf([
        'mode' => 'utf-8',
        'format' => 'A4',
        'orientation' => 'P',
        'margin_left' => 8,
        'margin_right' => 8,
        'margin_top' => 8,
        'margin_bottom' => 8,
        'margin_header' => 0,
        'margin_footer' => 0
    ]);
    
    // En-tête du PDF
    $html = '
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.2;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 12px;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 8px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 18px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .header h2 {
            color: #7f8c8d;
            margin: 2px 0;
            font-size: 12px;
        }
        .two-column {
            display: table;
            width: 100%;
            margin-bottom: 8px;
        }
        .column {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 10px;
        }
        .column:last-child {
            padding-right: 0;
            padding-left: 10px;
        }
        .info-section {
            margin-bottom: 8px;
            background-color: #fdfdfd;
            padding: 6px;
            border: 1px solid #e9ecef;
        }
        .info-section h3 {
            color: #34495e;
            border-bottom: 1px solid #3498db;
            padding-bottom: 2px;
            margin: 0 0 6px 0;
            font-size: 11px;
            font-weight: bold;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 4px;
        }
        .info-table td {
            padding: 3px 4px;
            border: 1px solid #ddd;
            vertical-align: top;
            font-size: 9px;
        }
        .info-table .label {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 35%;
            color: #34495e;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8px;
        }
        .details-table th, .details-table td {
            padding: 4px 3px;
            border: 1px solid #ddd;
            text-align: left;
            font-size: 8px;
        }
        .details-table th {
            background-color: #667eea;
            color: white;
            font-weight: bold;
            font-size: 8px;
            text-transform: uppercase;
        }
        .details-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .total-section {
            margin-top: 8px;
            background-color: #f8f9fa;
            padding: 6px;
            border-left: 3px solid #28a745;
        }
        .total-row {
            margin-bottom: 2px;
            display: table;
            width: 100%;
        }
        .total-label {
            font-weight: bold;
            color: #2c3e50;
            display: table-cell;
            width: 70%;
            font-size: 9px;
        }
        .total-value {
            font-weight: bold;
            color: #27ae60;
            display: table-cell;
            text-align: right;
            font-size: 9px;
        }
        .footer {
            margin-top: 10px;
            text-align: center;
            font-size: 7px;
            color: #7f8c8d;
            border-top: 1px solid #bdc3c7;
            padding-top: 5px;
        }
        .status-badge {
            padding: 2px 6px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            font-size: 8px;
            text-transform: uppercase;
        }
        .status-en-cours { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
        .status-en-attente { background: linear-gradient(135deg, #ffc107, #fd7e14); color: #000; }
        .status-facture { background: linear-gradient(135deg, #007bff, #6610f2); }
        .status-paye { background: linear-gradient(135deg, #28a745, #20c997); }
        .status-annule { background: linear-gradient(135deg, #dc3545, #e83e8c); }
    </style>
    
    <div class="header">
        <h1>ERP CACAO</h1>
        <h2>Système de Gestion des Ventes</h2>
        <h2 style="color: #e74c3c; font-weight: bold;">FACTURE DE VENTE</h2>
    </div>';
    
    // Fonction pour obtenir le badge de statut
    function getStatutBadge($statut) {
        $badges = [
            'EN COURS' => '<span class="status-badge status-en-cours">En Cours</span>',
            'EN ATTENTE' => '<span class="status-badge status-en-attente">En Attente</span>',
            'FACTURE' => '<span class="status-badge status-facture">Facturé</span>',
            'PAYE' => '<span class="status-badge status-paye">Payé</span>',
            'ANNULE' => '<span class="status-badge status-annule">Annulé</span>'
        ];
        return $badges[$statut] ?? '<span class="status-badge">Inconnu</span>';
    }
    
    // Layout en deux colonnes pour optimiser l'espace
    $html .= '
    <div class="two-column">
        <div class="column">
            <div class="info-section">
                <h3>📋 Informations de la Vente</h3>
                <table class="info-table">
                    <tr>
                        <td class="label">Référence:</td>
                        <td>' . htmlspecialchars($vente['facture_numero'] ?: 'VTE-' . $vente['id']) . '</td>
                    </tr>
                    <tr>
                        <td class="label">Date Vente:</td>
                        <td>' . date('d/m/Y', strtotime($vente['date_vente'])) . '</td>
                    </tr>
                    <tr>
                        <td class="label">Date Facture:</td>
                        <td>' . ($vente['facture_date'] ? date('d/m/Y', strtotime($vente['facture_date'])) : 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td class="label">Statut:</td>
                        <td>' . getStatutBadge($vente['statut']) . '</td>
                    </tr>
                </table>
            </div>
            <div class="info-section">
                <h3>📄 Documents</h3>
                <table class="info-table">
                    <tr>
                        <td class="label">N° Domiciliation:</td>
                        <td>' . htmlspecialchars($vente['n_domiciliation'] ?: 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td class="label">DAU N°:</td>
                        <td>' . htmlspecialchars($vente['dau_numero'] ?: 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td class="label">Lieu Export:</td>
                        <td>' . htmlspecialchars($vente['lieux_exportation'] ?: 'N/A') . '</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="column">
            <div class="info-section">
                <h3>👤 Informations Client</h3>
                <table class="info-table">
                    <tr>
                        <td class="label">Nom:</td>
                        <td>' . htmlspecialchars($vente['client_nom']) . '</td>
                    </tr>
                    <tr>
                        <td class="label">Type:</td>
                        <td>' . htmlspecialchars($vente['type_client']) . '</td>
                    </tr>';

    if ($vente['client_adresse']) {
        $html .= '
                    <tr>
                        <td class="label">Adresse:</td>
                        <td>' . htmlspecialchars($vente['client_adresse']) . '</td>
                    </tr>';
    }

    if ($vente['client_telephone']) {
        $html .= '
                    <tr>
                        <td class="label">Téléphone:</td>
                        <td>' . htmlspecialchars($vente['client_telephone']) . '</td>
                    </tr>';
    }

    if ($vente['client_nif']) {
        $html .= '
                    <tr>
                        <td class="label">NIF:</td>
                        <td>' . htmlspecialchars($vente['client_nif']) . '</td>
                    </tr>';
    }

    if ($vente['client_stat']) {
        $html .= '
                    <tr>
                        <td class="label">STAT:</td>
                        <td>' . htmlspecialchars($vente['client_stat']) . '</td>
                    </tr>';
    }

    $html .= '
                </table>
            </div>
        </div>
    </div>';
    
    // Détails des produits - layout compact
    $html .= '
    <div class="info-section">
        <h3>📦 Produits</h3>
        <table class="details-table">
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>Grade/Qualité</th>
                    <th class="text-right">Qté (T)</th>
                    <th class="text-right">Lots</th>
                    <th>BL/Conteneur</th>
                    <th>Expédition</th>
                </tr>
            </thead>
            <tbody>';
    
    $totalQuantite = 0;
    
    foreach ($details as $detail) {
        $quantite = floatval($detail['qte_tonnes'] ?? 0);
        $totalQuantite += $quantite;

        // Combiner grade et qualité
        $gradeQualite = trim(($detail['grade'] ?: '') . ' ' . ($detail['qualite'] ?: ''));
        if (empty($gradeQualite)) $gradeQualite = 'N/A';

        // Combiner BL et conteneur
        $blConteneur = trim(($detail['bl'] ?: '') . ' / ' . ($detail['conteneur'] ?: ''));
        if ($blConteneur === ' / ') $blConteneur = 'N/A';

        $html .= '
                <tr>
                    <td>' . htmlspecialchars($detail['produit_nom']) . '</td>
                    <td>' . htmlspecialchars($gradeQualite) . '</td>
                    <td class="text-right">' . number_format($quantite, 2, ',', ' ') . '</td>
                    <td class="text-right">' . ($detail['nbre_lot'] ?: '-') . '</td>
                    <td>' . htmlspecialchars($blConteneur) . '</td>
                    <td>' . htmlspecialchars($detail['expedition'] ?: '-') . '</td>
                </tr>';
    }
    
    $html .= '
            </tbody>
        </table>
    </div>';
    
    // Totaux compacts
    $html .= '
    <div class="total-section">
        <div class="total-row">
            <span class="total-label">Qté Totale:</span>
            <span class="total-value">' . number_format($totalQuantite, 2, ',', ' ') . ' T</span>
        </div>
        <div class="total-row">
            <span class="total-label">Montant Total:</span>
            <span class="total-value">' . number_format($vente['total_montant'], 0, ',', ' ') . ' Ar</span>
        </div>';

    if ($vente['valeur_euro']) {
        $html .= '
        <div class="total-row">
            <span class="total-label">Valeur EUR:</span>
            <span class="total-value">' . number_format($vente['valeur_euro'], 2, ',', ' ') . ' €</span>
        </div>';
    }

    if ($vente['cours_devise']) {
        $html .= '
        <div class="total-row">
            <span class="total-label">Cours:</span>
            <span class="total-value">' . number_format($vente['cours_devise'], 2, ',', ' ') . ' Ar/€</span>
        </div>';
    }

    $html .= '
    </div>';
    
    // Footer compact
    $html .= '
    <div class="footer">
        <strong>ERP CACAO</strong> | Facture générée le ' . date('d/m/Y H:i') . ' | Document officiel de vente
    </div>';
    
    $mpdf->WriteHTML($html);
    
    // Nom du fichier
    $filename = 'Facture_Vente_' . ($vente['facture_numero'] ?: $vente['id']) . '_' . date('Y-m-d') . '.pdf';
    
    // Envoyer le PDF au navigateur
    $mpdf->Output($filename, 'I');
    
} catch (Exception $e) {
    die('Erreur lors de la génération du PDF: ' . $e->getMessage());
}
?>
