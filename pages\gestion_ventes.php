<?php
session_start();
require_once dirname(__DIR__) . '/config/config.php';
require_once CONFIG_PATH . '/database.php';
require_once INCLUDES_PATH . '/auth.php';
require_once INCLUDES_PATH . '/rbac.php';

// Vérification de l'authentification
$auth = new Auth();
$auth->checkAuth();

// Vérification des permissions
$rbac = new RBAC();
$rbac->requirePermission('ventes.view');

$currentUser = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Gestion des Ventes</title>
    <!-- Bootstrap Cerulean -->
    <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.2/dist/cerulean/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- CSS personnalisé -->
    <style>
        .badge {
            font-size: 0.75em;
            padding: 0.35em 0.65em;
        }
        .btn-sm i {
            font-size: 0.875em;
        }
        .table th {
            font-size: 0.875rem;
            font-weight: 600;
        }
        .table td {
            font-size: 0.875rem;
        }
        .btn-group .btn {
            margin-right: 0.25rem;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .table td:last-child {
            white-space: nowrap;
            min-width: 200px;
        }
        .table td:last-child .btn {
            margin: 0 2px;
        }
        .text-center .badge {
            display: inline-block;
            min-width: 50px;
        }
        .status-badge {
            font-size: 0.7em;
            padding: 0.4em 0.8em;
        }
        .sales-step {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .sales-step .step-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-size: 0.8rem;
        }
        .step-completed {
            background-color: #28a745;
            color: white;
        }
        .step-current {
            background-color: #007bff;
            color: white;
        }
        .step-pending {
            background-color: #6c757d;
            color: white;
        }
        .sales-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .sales-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .currency-input {
            position: relative;
        }
        .currency-input::after {
            content: "Ar";
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            pointer-events: none;
        }

        /* Styles pour les cartes de produits */
        .product-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            height: 100%;
        }

        .product-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
            transform: translateY(-2px);
        }

        .product-card.selected {
            border-color: #28a745;
            background-color: #f8fff9;
            box-shadow: 0 4px 12px rgba(40,167,69,0.2);
        }

        .product-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            padding: 12px 15px;
            border-radius: 7px 7px 0 0;
        }

        .product-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 4px;
            font-size: 0.95rem;
        }

        .product-subtitle {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 0;
        }

        .product-body {
            padding: 15px;
        }

        .lot-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .lot-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .lot-item.selected {
            background: #e8f5e8;
            border-color: #28a745;
        }

        .lot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }

        .lot-number {
            font-weight: 600;
            color: #495057;
            font-size: 0.85rem;
        }

        .stock-badge {
            font-size: 0.75rem;
            padding: 3px 8px;
            border-radius: 12px;
        }

        .stock-high {
            background-color: #d4edda;
            color: #155724;
        }

        .stock-medium {
            background-color: #fff3cd;
            color: #856404;
        }

        .stock-low {
            background-color: #f8d7da;
            color: #721c24;
        }

        .lot-details {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .quantity-selector {
            margin-top: 8px;
            display: none;
        }

        .quantity-selector.show {
            display: block;
        }

        .btn-select-lot {
            font-size: 0.75rem;
            padding: 4px 12px;
        }

        /* Conversion and validation styles */
        .conversion-display {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 4px 8px;
            font-weight: 500;
        }

        .stock-validation-message {
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
            border-radius: 4px;
            padding: 4px 8px;
            font-weight: 500;
        }

        .quantity-input.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        /* Enhanced lot item styling */
        .lot-item.selected {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">
                <i class="fas fa-chart-line"></i> Gestion des Ventes
            </h6>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalVente" title="Nouvelle vente">
                    <i class="fas fa-plus"></i> Nouvelle Vente
                </button>
                <button class="btn btn-outline-danger" id="btnCancelVentes" title="Annuler les ventes sélectionnées">
                    <i class="fas fa-times"></i> Annuler
                </button>
            </div>
        </div>

        <!-- Filtres -->
        <div class="card mb-3 sales-card">
            <div class="card-header py-2">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i> Filtres
                </h6>
            </div>
            <div class="card-body py-2">
                <div class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label small">Statut</label>
                        <select id="filterStatus" class="form-select form-select-sm">
                            <option value="">Tous les statuts</option>
                            <option value="EN COURS">En cours</option>
                            <option value="EN ATTENTE">En attente</option>
                            <option value="FACTURE">Facturé</option>
                            <option value="PAYE">Payé</option>
                            <option value="ANNULE">Annulé</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">Date début</label>
                        <input type="date" id="filterDateFrom" class="form-control form-control-sm">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">Date fin</label>
                        <input type="date" id="filterDateTo" class="form-control form-control-sm">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label small">Client</label>
                        <select id="filterClient" class="form-select form-select-sm">
                            <option value="">Tous les clients</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">&nbsp;</label>
                        <div class="d-flex gap-1">
                            <button class="btn btn-outline-primary btn-sm" id="btnApplyFilters" title="Appliquer les filtres">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" id="btnResetFilters" title="Réinitialiser les filtres">
                                <i class="fas fa-undo"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">
            <!-- Table Ventes -->
            <div class="col-12">
                <div class="card shadow-sm border-0 sales-card">
                    <div class="card-header py-2">
                        <h6 class="mb-0">Liste des Ventes</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                            <table id="tableVentes" class="table table-sm table-hover align-middle table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th><input type="checkbox" id="selectAll"></th>
                                        <th>Référence</th>
                                        <th>Client</th>
                                        <th>Date Vente</th>
                                        <th>Date Facture</th>
                                        <th>Statut</th>
                                        <th>Mont. Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Données chargées dynamiquement -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistiques récapitulatives -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card sales-card">
                    <div class="card-header py-2">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar"></i> Statistiques
                        </h6>
                    </div>
                    <div class="card-body py-2">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-primary" id="statTotalVentes">0</div>
                                    <small class="text-muted">Total Ventes</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-success" id="statMontantTotal">0 Ar</div>
                                    <small class="text-muted">Montant Total</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-info" id="statClients">0</div>
                                    <small class="text-muted">Clients</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-warning" id="statEnAttente">0</div>
                                    <small class="text-muted">En Attente</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-danger" id="statAFacturer">0</div>
                                    <small class="text-muted">À Facturer</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-secondary" id="statPayes">0</div>
                                    <small class="text-muted">Payés</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Vente (Plein écran) -->
    <div class="modal fade" id="modalVente" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalVenteTitle">
                        <i class="fas fa-chart-line"></i> Nouvelle Vente
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Informations générales -->
                        <div class="col-md-3">
                            <div class="card sales-card">
                                <div class="card-header">
                                    <h6 class="mb-0">Informations Générales</h6>
                                </div>
                                <div class="card-body">
                                    <form id="formVenteEntete">
                                        <div class="mb-3">
                                            <label class="form-label">Référence Vente</label>
                                            <input type="text" id="referenceVente" class="form-control" readonly>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Client *</label>
                                            <select id="clientVente" class="form-select" required>
                                                <option value="">Sélectionner un client</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Date de Vente</label>
                                            <input type="date" id="dateVente" class="form-control">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Date de Facture</label>
                                            <input type="date" id="dateFacture" class="form-control">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">N° Facture</label>
                                            <input type="text" id="numeroFacture" class="form-control" placeholder="Numéro de facture">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Lieu d'Exportation</label>
                                            <input type="text" id="lieuExportation" class="form-control" placeholder="Lieu d'exportation">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">N° Domiciliation</label>
                                            <input type="text" id="nDomiciliation" class="form-control" placeholder="Numéro de domiciliation">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">N° DAU</label>
                                            <input type="text" id="dauNumero" class="form-control" placeholder="Numéro DAU">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Date DAU</label>
                                            <input type="date" id="dauDate" class="form-control">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Détails des produits -->
                        <div class="col-md-9">
                            <div class="card sales-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Détails des Produits</h6>
                                    <button class="btn btn-sm btn-success" id="btnAddProduitVente" title="Ajouter un produit">
                                        <i class="fas fa-plus"></i> Ajouter Produit
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table id="tableDetailsVente" class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Produit</th>
                                                    <th>Dépôt</th>
                                                    <th>Grade</th>
                                                    <th>Qualité</th>
                                                    <th>Qté (Tonnes)</th>
                                                    <th>Nb Lots</th>
                                                    <th>BL</th>
                                                    <th>Conteneur</th>
                                                    <th>Seal</th>
                                                    <th>Lots</th>
                                                    <th>Expédition</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Lignes ajoutées dynamiquement -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Section de sélection de produits (collapsible) -->
                                    <div id="productSelectionSection" class="mt-3" style="display: none;">
                                        <div class="border-top pt-3">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-boxes"></i> Sélectionner un Produit
                                                </h6>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="btnCancelProductSelection">
                                                    <i class="fas fa-times"></i> Annuler
                                                </button>
                                            </div>

                                            <!-- Filtres de recherche -->
                                            <div class="row mb-3">
                                                <div class="col-md-3">
                                                    <label class="form-label small">Rechercher</label>
                                                    <input type="text" id="searchProduit" class="form-control form-control-sm" placeholder="Nom du produit...">
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label small">Type</label>
                                                    <select id="filterType" class="form-select form-select-sm">
                                                        <option value="">Tous les types</option>
                                                        <option value="Standard">Standard</option>
                                                        <option value="Superieur">Supérieur</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label small">Certification</label>
                                                    <select id="filterCertification" class="form-select form-select-sm">
                                                        <option value="">Toutes</option>
                                                        <option value="Bio">Bio</option>
                                                        <option value="Conventionnel">Conventionnel</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label small">Dépôt</label>
                                                    <select id="filterDepot" class="form-select form-select-sm">
                                                        <option value="">Tous les dépôts</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label small">Stock</label>
                                                    <select id="filterStock" class="form-select form-select-sm">
                                                        <option value="">Tous</option>
                                                        <option value="available">Disponible uniquement</option>
                                                        <option value="low">Stock faible</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-1">
                                                    <label class="form-label small">&nbsp;</label>
                                                    <button class="btn btn-outline-secondary btn-sm w-100" id="btnResetFilters" title="Réinitialiser">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Zone d'affichage des produits -->
                                            <div id="produitsContainer" class="row g-3" style="max-height: 400px; overflow-y: auto;">
                                                <!-- Les cartes de produits seront générées ici -->
                                            </div>

                                            <!-- Message si aucun produit -->
                                            <div id="noProductsMessage" class="text-center py-5 d-none">
                                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">Aucun produit trouvé</h5>
                                                <p class="text-muted">Essayez de modifier vos critères de recherche</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Totaux -->
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6>Résumé</h6>
                                                    <p class="mb-1">Montant HT: <span id="montantHT">0 Ar</span></p>
                                                    <p class="mb-1">Remise: <span id="montantRemise">0 Ar</span></p>
                                                    <hr>
                                                    <p class="mb-0 fw-bold">Total TTC: <span id="montantTTC">0 Ar</span></p>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveVente">
                        <i class="fas fa-save"></i> <span id="btnSaveText">Enregistrer la Vente</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Détails de Vente -->
    <div class="modal fade" id="modalDetailsVente" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye"></i> Détails de la Vente
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="detailsContent">
                        <!-- Contenu des détails chargé dynamiquement -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Suivi de Vente -->
    <div class="modal fade" id="modalSuiviVente" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-route"></i> Suivi de Vente
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="suiviContent">
                        <!-- Contenu du suivi chargé dynamiquement -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Paiement supprimé - utiliser gestion_paiements_ventes.php -->

    <!-- JS Bootstrap + DataTables + SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/gestion_ventes.js?v=<?php echo date('YmdHis'); ?>"></script>

</body>

</html>
