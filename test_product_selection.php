<?php
// Test script pour vérifier et ajouter des données de test pour la sélection de produits

require_once dirname(__DIR__) . '/config/config.php';
require_once CONFIG_PATH . '/database.php';

echo "<h2>Test de la sélection de produits améliorée</h2>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>1. Vérification des produits existants</h3>";
    
    // Vérifier les produits
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits WHERE classe = 'PRODUITS'");
    $produitCount = $stmt->fetch()['count'];
    echo "✅ Produits disponibles: <strong>$produitCount</strong><br>";
    
    // Vérifier le stock
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits_stock WHERE quantite > 0");
    $stockCount = $stmt->fetch()['count'];
    echo "✅ Entrées de stock avec quantité > 0: <strong>$stockCount</strong><br>";
    
    if ($stockCount == 0) {
        echo "<h3>2. Ajout de données de stock de test</h3>";
        
        // Récupérer les produits existants
        $stmt = $pdo->query("SELECT id, nom FROM produits WHERE classe = 'PRODUITS' LIMIT 3");
        $produits = $stmt->fetchAll();
        
        if (count($produits) > 0) {
            foreach ($produits as $produit) {
                // Ajouter plusieurs lots pour chaque produit
                for ($i = 1; $i <= 3; $i++) {
                    $lotNumero = "LOT-" . $produit['id'] . "-" . str_pad($i, 3, '0', STR_PAD_LEFT);
                    $quantite = rand(50, 500); // Quantité aléatoire entre 50 et 500 kg
                    $depotId = rand(1, 2); // Dépôt 1 ou 2
                    
                    $stmt = $pdo->prepare("INSERT INTO produits_stock (
                        produit_id, depot_id, quantite, unite_stock_id, lot_numero, 
                        date_entree, date_creation, cree_par
                    ) VALUES (?, ?, ?, 3, ?, NOW(), NOW(), 'test_system')");
                    
                    $stmt->execute([$produit['id'], $depotId, $quantite, $lotNumero]);
                    
                    echo "✅ Stock ajouté: {$produit['nom']} - Lot $lotNumero - $quantite kg<br>";
                }
            }
        }
    }
    
    echo "<h3>3. Résumé des données disponibles</h3>";
    
    // Afficher un résumé des produits avec stock
    $stmt = $pdo->query("
        SELECT 
            p.id,
            p.nom,
            p.type,
            p.certification,
            c.libelle as categorie,
            COUNT(ps.id) as nb_lots,
            SUM(ps.quantite) as stock_total,
            us.libelle as unite
        FROM produits p
        LEFT JOIN categorie c ON p.categorie_id = c.id
        LEFT JOIN unites us ON p.unite_stock_id = us.id
        LEFT JOIN produits_stock ps ON p.id = ps.produit_id AND ps.quantite > 0
        WHERE p.classe = 'PRODUITS'
        GROUP BY p.id
        ORDER BY p.nom
    ");
    
    $produits = $stmt->fetchAll();
    
    if (count($produits) > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>Produit</th><th>Type</th><th>Certification</th><th>Catégorie</th><th>Nb Lots</th><th>Stock Total</th><th>Unité</th>";
        echo "</tr>";
        
        foreach ($produits as $produit) {
            echo "<tr>";
            echo "<td>{$produit['nom']}</td>";
            echo "<td>{$produit['type']}</td>";
            echo "<td>{$produit['certification']}</td>";
            echo "<td>{$produit['categorie']}</td>";
            echo "<td>{$produit['nb_lots']}</td>";
            echo "<td>" . number_format($produit['stock_total'], 2) . "</td>";
            echo "<td>{$produit['unite']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<h3>4. Détail des lots par produit</h3>";
    
    // Afficher le détail des lots
    $stmt = $pdo->query("
        SELECT 
            p.nom as produit_nom,
            ps.lot_numero,
            ps.quantite,
            d.libelle as depot_nom,
            ps.date_entree,
            ps.date_expiration
        FROM produits_stock ps
        LEFT JOIN produits p ON ps.produit_id = p.id
        LEFT JOIN depot d ON ps.depot_id = d.id
        WHERE ps.quantite > 0 AND p.classe = 'PRODUITS'
        ORDER BY p.nom, ps.lot_numero
    ");
    
    $lots = $stmt->fetchAll();
    
    if (count($lots) > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>Produit</th><th>Lot</th><th>Quantité</th><th>Dépôt</th><th>Date Entrée</th><th>Date Expiration</th>";
        echo "</tr>";
        
        foreach ($lots as $lot) {
            echo "<tr>";
            echo "<td>{$lot['produit_nom']}</td>";
            echo "<td>{$lot['lot_numero']}</td>";
            echo "<td>" . number_format($lot['quantite'], 2) . "</td>";
            echo "<td>{$lot['depot_nom']}</td>";
            echo "<td>{$lot['date_entree']}</td>";
            echo "<td>" . ($lot['date_expiration'] ?: 'N/A') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<h3>5. Test terminé</h3>";
    echo "<p>✅ Les données sont prêtes pour tester l'interface de sélection de produits.</p>";
    echo "<p><a href='gestion_ventes.php' target='_blank'>🔗 Ouvrir la gestion des ventes</a></p>";
    
} catch (PDOException $e) {
    echo "❌ Erreur de base de données: " . $e->getMessage();
}
?>
